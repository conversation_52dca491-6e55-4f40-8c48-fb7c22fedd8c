"""
核心模块测试

测试核心模块的功能，包括：
- Application类测试
- BaseModel类测试
- BaseController类测试
- ThemeManager类测试
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import Mock, patch
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QSettings

from core.application import Application, create_application
from core.models import BaseModel, FieldValidator, ModelManager
from core.controllers import BaseController, CRUDController, ModelUpdateCommand
from core.theme_manager import ThemeManager


class TestApplication:
    """Application类测试"""
    
    @pytest.fixture
    def app(self):
        """创建测试应用程序"""
        app = create_application(['test'])
        yield app
        app.quit()
    
    def test_application_creation(self, app):
        """测试应用程序创建"""
        assert isinstance(app, Application)
        assert app.applicationName() == "PySide6开发框架"
        assert app.applicationVersion() == "1.0.0"
    
    def test_theme_manager_initialization(self, app):
        """测试主题管理器初始化"""
        theme_manager = app.get_theme_manager()
        assert isinstance(theme_manager, ThemeManager)
        assert theme_manager.get_current_theme() in ["light", "dark"]
    
    def test_settings_initialization(self, app):
        """测试设置初始化"""
        settings = app.get_settings()
        assert isinstance(settings, QSettings)
    
    def test_main_window_management(self, app):
        """测试主窗口管理"""
        mock_window = Mock()
        app.set_main_window(mock_window)
        assert app.get_main_window() == mock_window


class TestFieldValidator:
    """FieldValidator类测试"""
    
    def test_string_validation(self):
        """测试字符串验证"""
        validator = FieldValidator(str, required=True, min_length=3, max_length=10)
        
        # 有效值
        is_valid, error = validator.validate("hello")
        assert is_valid
        assert error == ""
        
        # 必填验证
        is_valid, error = validator.validate("")
        assert not is_valid
        assert "必填项" in error
        
        # 长度验证
        is_valid, error = validator.validate("hi")
        assert not is_valid
        assert "长度不能少于" in error
        
        is_valid, error = validator.validate("very long string")
        assert not is_valid
        assert "长度不能超过" in error
    
    def test_integer_validation(self):
        """测试整数验证"""
        validator = FieldValidator(int, default=0)
        
        # 有效值
        is_valid, error = validator.validate(42)
        assert is_valid
        
        # 类型转换
        is_valid, error = validator.validate("123")
        assert is_valid
        
        # 无效类型
        is_valid, error = validator.validate("not a number")
        assert not is_valid
    
    def test_choices_validation(self):
        """测试选择项验证"""
        validator = FieldValidator(str, choices=["red", "green", "blue"])
        
        # 有效选择
        is_valid, error = validator.validate("red")
        assert is_valid
        
        # 无效选择
        is_valid, error = validator.validate("yellow")
        assert not is_valid
        assert "必须在" in error


class TestBaseModel:
    """BaseModel类测试"""
    
    class TestModel(BaseModel):
        """测试模型类"""
        
        def _define_fields(self):
            super()._define_fields()
            self.add_field('name', str, required=True, max_length=50)
            self.add_field('age', int, default=0)
            self.add_field('email', str)
    
    def test_model_creation(self):
        """测试模型创建"""
        model = self.TestModel(name="Test User", age=25)
        
        assert model.get_value('name') == "Test User"
        assert model.get_value('age') == 25
        assert model.get_value('id') == 0
        assert isinstance(model.get_value('created_at'), datetime)
    
    def test_field_validation(self):
        """测试字段验证"""
        model = self.TestModel()
        
        # 有效值
        assert model.set_value('name', 'Valid Name')
        assert model.get_value('name') == 'Valid Name'
        
        # 无效值（超长）
        long_name = 'x' * 100
        assert not model.set_value('name', long_name)
    
    def test_model_validation(self):
        """测试模型验证"""
        model = self.TestModel(name="Test", age=25)
        
        is_valid, errors = model.validate()
        assert is_valid
        assert len(errors) == 0
        
        # 移除必填字段
        model.set_value('name', '', validate=False)
        is_valid, errors = model.validate()
        assert not is_valid
        assert 'name' in errors
    
    def test_serialization(self):
        """测试序列化"""
        model = self.TestModel(name="Test User", age=25)
        
        # 转换为字典
        data = model.to_dict()
        assert data['name'] == "Test User"
        assert data['age'] == 25
        
        # 转换为JSON
        json_str = model.to_json()
        assert '"name": "Test User"' in json_str
        
        # 从字典加载
        new_model = self.TestModel()
        new_model.from_dict(data)
        assert new_model.get_value('name') == "Test User"
        assert new_model.get_value('age') == 25
    
    def test_model_cloning(self):
        """测试模型克隆"""
        original = self.TestModel(name="Original", age=30)
        clone = original.clone()
        
        assert clone.get_value('name') == "Original"
        assert clone.get_value('age') == 30
        assert clone is not original


class TestModelManager:
    """ModelManager类测试"""
    
    def test_model_management(self):
        """测试模型管理"""
        manager = ModelManager()
        model = BaseModel()
        
        # 添加模型
        manager.add_model("test_model", model)
        assert manager.get_model("test_model") == model
        
        # 获取所有模型
        all_models = manager.get_all_models()
        assert "test_model" in all_models
        
        # 移除模型
        assert manager.remove_model("test_model")
        assert manager.get_model("test_model") is None
    
    def test_model_signals(self):
        """测试模型信号"""
        manager = ModelManager()
        model = BaseModel()
        
        # 模拟信号连接
        model_added_called = False
        model_removed_called = False
        
        def on_model_added(name, model):
            nonlocal model_added_called
            model_added_called = True
        
        def on_model_removed(name):
            nonlocal model_removed_called
            model_removed_called = True
        
        manager.model_added.connect(on_model_added)
        manager.model_removed.connect(on_model_removed)
        
        manager.add_model("test", model)
        manager.remove_model("test")
        
        # 注意：在测试环境中信号可能不会立即触发
        # 这里主要测试方法调用不出错


class TestBaseController:
    """BaseController类测试"""
    
    class TestController(BaseController):
        """测试控制器类"""
        
        def initialize(self):
            self.set_status("ready")
            return True
        
        def cleanup(self):
            self.set_status("cleaned")
    
    def test_controller_creation(self):
        """测试控制器创建"""
        model = BaseModel()
        controller = self.TestController(model)
        
        assert controller.get_model() == model
        assert controller.get_status() == "initialized"
    
    def test_controller_initialization(self):
        """测试控制器初始化"""
        controller = self.TestController()
        
        result = controller.initialize()
        assert result is True
        assert controller.get_status() == "ready"
    
    def test_undo_redo_functionality(self):
        """测试撤销重做功能"""
        model = BaseModel()
        controller = self.TestController(model)
        
        # 执行命令
        command = ModelUpdateCommand(model, "test_field", "new_value", "old_value")
        controller.execute_command(command)
        
        # 检查撤销重做状态
        assert controller.can_undo()
        assert not controller.can_redo()
        
        # 撤销
        controller.undo()
        assert not controller.can_undo()
        assert controller.can_redo()
        
        # 重做
        controller.redo()
        assert controller.can_undo()
        assert not controller.can_redo()
    
    def test_event_handling(self):
        """测试事件处理"""
        controller = self.TestController()
        
        event_handled = False
        
        def event_handler(*args, **kwargs):
            nonlocal event_handled
            event_handled = True
        
        # 添加事件处理器
        controller.add_event_handler("test_event", event_handler)
        
        # 触发事件
        controller.emit_event("test_event")
        
        # 移除事件处理器
        assert controller.remove_event_handler("test_event", event_handler)


class TestCRUDController:
    """CRUDController类测试"""
    
    def test_crud_operations(self):
        """测试CRUD操作"""
        controller = CRUDController()
        controller.initialize()
        
        # 创建项目
        item_data = {"name": "Test Item", "description": "Test Description"}
        item = controller.create_item(item_data)
        assert item is not None
        assert item.get_value("name") == "Test Item"
        
        # 读取项目
        item_id = item.get_value("id")
        retrieved_item = controller.read_item(item_id)
        assert retrieved_item == item
        
        # 更新项目
        update_data = {"name": "Updated Item"}
        success = controller.update_item(item_id, update_data)
        assert success
        assert item.get_value("name") == "Updated Item"
        
        # 列出所有项目
        items = controller.list_items()
        assert len(items) > 0
        assert item in items
        
        # 删除项目
        success = controller.delete_item(item_id)
        assert success
        assert controller.read_item(item_id) is None
    
    def test_search_functionality(self):
        """测试搜索功能"""
        controller = CRUDController()
        controller.initialize()
        
        # 创建测试数据
        controller.create_item({"name": "Apple", "description": "Fruit"})
        controller.create_item({"name": "Banana", "description": "Yellow fruit"})
        controller.create_item({"name": "Carrot", "description": "Vegetable"})
        
        # 搜索测试
        results = controller.search_items("fruit")
        assert len(results) == 2  # Apple和Banana
        
        results = controller.search_items("Apple", "name")
        assert len(results) == 1
        assert results[0].get_value("name") == "Apple"


class TestThemeManager:
    """ThemeManager类测试"""
    
    @pytest.fixture
    def app(self):
        """创建测试应用程序"""
        app = create_application(['test'])
        yield app
        app.quit()
    
    def test_theme_manager_creation(self, app):
        """测试主题管理器创建"""
        theme_manager = ThemeManager(app)
        
        assert theme_manager.get_current_theme() == "light"
        available_themes = theme_manager.get_available_themes()
        assert "light" in available_themes
        assert "dark" in available_themes
    
    def test_theme_switching(self, app):
        """测试主题切换"""
        theme_manager = ThemeManager(app)
        
        # 切换到深色主题
        success = theme_manager.set_theme("dark")
        assert success
        assert theme_manager.get_current_theme() == "dark"
        
        # 切换回浅色主题
        success = theme_manager.set_theme("light")
        assert success
        assert theme_manager.get_current_theme() == "light"
        
        # 切换主题（toggle）
        new_theme = theme_manager.toggle_theme()
        assert new_theme == "dark"
        assert theme_manager.get_current_theme() == "dark"
    
    def test_theme_colors(self, app):
        """测试主题颜色"""
        theme_manager = ThemeManager(app)
        
        # 获取颜色
        primary_color = theme_manager.get_theme_color("primary")
        assert primary_color.startswith("#")
        
        background_color = theme_manager.get_theme_color("background", "light")
        assert background_color == "#F5F5F7"
        
        dark_background = theme_manager.get_theme_color("background", "dark")
        assert dark_background == "#1E1E1E"
    
    def test_invalid_theme(self, app):
        """测试无效主题"""
        theme_manager = ThemeManager(app)
        
        success = theme_manager.set_theme("invalid_theme")
        assert not success
        assert theme_manager.get_current_theme() == "light"  # 应该保持原主题


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])