"""
仪表盘界面

实现应用程序的仪表盘，包括：
- 卡片式布局
- 实时数据图表
- 统计信息显示
- 快速操作按钮
- 响应式设计
"""

import logging
import random
from typing import Dict, List, Any
from datetime import datetime, timedelta

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QPushButton, QFrame, QScrollArea,
                               QProgressBar, QGroupBox, QSizePolicy)
from PySide6.QtCore import Qt, Signal, QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QFont, QPalette, QColor, QPainter, QPen, QBrush
from PySide6.QtCharts import QChart, QChartView, QLineSeries, QPieSeries, QBarSeries, QBarSet

from .base_view import BaseView


class StatCard(QFrame):
    """
    统计卡片
    
    显示单个统计指标的卡片组件。
    """
    
    def __init__(self, title: str, value: str, icon: str = "", 
                 trend: float = 0.0, parent: QWidget = None):
        """
        初始化统计卡片
        
        Args:
            title: 卡片标题
            value: 显示值
            icon: 图标
            trend: 趋势（正数为上升，负数为下降）
            parent: 父组件
        """
        super().__init__(parent)
        
        self.title = title
        self.current_value = value
        self.icon = icon
        self.trend = trend
        
        self._setup_ui()
        self._setup_animation()
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        self.setFrameStyle(QFrame.Box)
        self.setFixedHeight(120)
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E5E5EA;
                border-radius: 8px;
                padding: 16px;
            }
            QFrame:hover {
                border-color: #007AFF;
                background-color: #F8F9FA;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(8)
        
        # 标题行
        title_layout = QHBoxLayout()
        
        # 图标
        if self.icon:
            icon_label = QLabel(self.icon)
            icon_label.setStyleSheet("font-size: 24px; color: #007AFF;")
            title_layout.addWidget(icon_label)
        
        # 标题
        title_label = QLabel(self.title)
        title_label.setStyleSheet("font-size: 14px; color: #8E8E93; font-weight: 500;")
        title_layout.addWidget(title_label, 1)
        
        layout.addLayout(title_layout)
        
        # 数值
        self.value_label = QLabel(self.current_value)
        self.value_label.setStyleSheet("font-size: 28px; color: #1D1D1F; font-weight: 600;")
        layout.addWidget(self.value_label)
        
        # 趋势
        if self.trend != 0:
            trend_text = f"{'↗' if self.trend > 0 else '↘'} {abs(self.trend):.1f}%"
            trend_color = "#34C759" if self.trend > 0 else "#FF3B30"
            
            self.trend_label = QLabel(trend_text)
            self.trend_label.setStyleSheet(f"font-size: 12px; color: {trend_color}; font-weight: 500;")
            layout.addWidget(self.trend_label)
        
        layout.addStretch()
    
    def _setup_animation(self) -> None:
        """设置动画效果"""
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(200)
        self.hover_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def update_value(self, new_value: str, new_trend: float = None) -> None:
        """
        更新卡片值
        
        Args:
            new_value: 新值
            new_trend: 新趋势
        """
        self.current_value = new_value
        self.value_label.setText(new_value)
        
        if new_trend is not None:
            self.trend = new_trend
            if hasattr(self, 'trend_label'):
                trend_text = f"{'↗' if self.trend > 0 else '↘'} {abs(self.trend):.1f}%"
                trend_color = "#34C759" if self.trend > 0 else "#FF3B30"
                self.trend_label.setText(trend_text)
                self.trend_label.setStyleSheet(f"font-size: 12px; color: {trend_color}; font-weight: 500;")
    
    def enterEvent(self, event) -> None:
        """鼠标进入事件"""
        self.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #007AFF;
                border-radius: 8px;
                padding: 16px;
            }
        """)
        super().enterEvent(event)
    
    def leaveEvent(self, event) -> None:
        """鼠标离开事件"""
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E5E5EA;
                border-radius: 8px;
                padding: 16px;
            }
        """)
        super().leaveEvent(event)


class ChartWidget(QWidget):
    """
    图表组件
    
    封装QChart的图表组件。
    """
    
    def __init__(self, chart_type: str = "line", parent: QWidget = None):
        """
        初始化图表组件
        
        Args:
            chart_type: 图表类型 (line, pie, bar)
            parent: 父组件
        """
        super().__init__(parent)
        
        self.chart_type = chart_type
        self.chart = QChart()
        self.chart_view = QChartView(self.chart)
        
        self._setup_ui()
        self._create_sample_data()
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 配置图表视图
        self.chart_view.setRenderHint(QPainter.Antialiasing)
        self.chart_view.setStyleSheet("""
            QChartView {
                background-color: white;
                border: 1px solid #E5E5EA;
                border-radius: 8px;
            }
        """)
        
        layout.addWidget(self.chart_view)
    
    def _create_sample_data(self) -> None:
        """创建示例数据"""
        if self.chart_type == "line":
            self._create_line_chart()
        elif self.chart_type == "pie":
            self._create_pie_chart()
        elif self.chart_type == "bar":
            self._create_bar_chart()
    
    def _create_line_chart(self) -> None:
        """创建折线图"""
        series = QLineSeries()
        series.setName("用户活跃度")
        
        # 生成示例数据
        now = datetime.now()
        for i in range(30):
            date = now - timedelta(days=29-i)
            value = 50 + random.randint(-20, 30) + i * 2
            series.append(i, value)
        
        self.chart.addSeries(series)
        self.chart.setTitle("过去30天用户活跃度趋势")
        self.chart.createDefaultAxes()
        self.chart.legend().setVisible(True)
    
    def _create_pie_chart(self) -> None:
        """创建饼图"""
        series = QPieSeries()
        
        # 示例数据
        data = [
            ("桌面端", 45.2),
            ("移动端", 32.8),
            ("Web端", 15.6),
            ("其他", 6.4)
        ]
        
        for name, value in data:
            slice_item = series.append(name, value)
            slice_item.setLabelVisible(True)
        
        self.chart.addSeries(series)
        self.chart.setTitle("用户设备分布")
        self.chart.legend().setVisible(True)
    
    def _create_bar_chart(self) -> None:
        """创建柱状图"""
        series = QBarSeries()
        
        # 创建数据集
        set0 = QBarSet("本月")
        set1 = QBarSet("上月")
        
        # 示例数据
        current_month = [20, 32, 45, 38, 52, 41, 35]
        last_month = [18, 28, 42, 35, 48, 38, 32]
        
        for value in current_month:
            set0.append(value)
        
        for value in last_month:
            set1.append(value)
        
        series.append(set0)
        series.append(set1)
        
        self.chart.addSeries(series)
        self.chart.setTitle("每周活跃用户对比")
        self.chart.createDefaultAxes()
        self.chart.legend().setVisible(True)
    
    def update_data(self, data: List[Any]) -> None:
        """
        更新图表数据
        
        Args:
            data: 新数据
        """
        # 子类可以重写此方法来更新特定类型的图表
        pass


class QuickActionButton(QPushButton):
    """
    快速操作按钮
    
    仪表盘上的快速操作按钮。
    """
    
    def __init__(self, icon: str, text: str, description: str = "", parent: QWidget = None):
        """
        初始化快速操作按钮
        
        Args:
            icon: 图标
            text: 按钮文本
            description: 描述文本
            parent: 父组件
        """
        super().__init__(parent)
        
        self.icon = icon
        self.button_text = text
        self.description = description
        
        self._setup_ui()
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        self.setFixedSize(120, 100)
        self.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #FFFFFF,
                                            stop: 1 #F8F8F8);
                border: 1px solid #E5E5EA;
                border-radius: 8px;
                text-align: center;
                padding: 8px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #F8F8F8,
                                            stop: 1 #F0F0F0);
                border-color: #007AFF;
            }
            QPushButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #F0F0F0,
                                            stop: 1 #E8E8E8);
            }
        """)
        
        # 设置按钮文本（包含图标和文字）
        button_text = f"{self.icon}\n{self.button_text}"
        if self.description:
            button_text += f"\n{self.description}"
        
        self.setText(button_text)


class Dashboard(BaseView):
    """
    仪表盘界面
    
    应用程序的主仪表盘，显示统计信息、图表和快速操作。
    """
    
    # 信号定义
    quick_action_clicked = Signal(str)  # 快速操作点击信号
    
    def __init__(self, parent: QWidget = None):
        """
        初始化仪表盘
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        # 数据更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_data)
        self.update_timer.start(5000)  # 每5秒更新一次
        
        # 统计卡片
        self.stat_cards: Dict[str, StatCard] = {}
        
        # 图表组件
        self.charts: Dict[str, ChartWidget] = {}
    
    def setup_ui(self) -> None:
        """设置用户界面"""
        content_widget = self.get_content_widget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # 滚动内容
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(20)
        
        # 欢迎区域
        self._create_welcome_section(scroll_layout)
        
        # 统计卡片区域
        self._create_stats_section(scroll_layout)
        
        # 图表区域
        self._create_charts_section(scroll_layout)
        
        # 快速操作区域
        self._create_quick_actions_section(scroll_layout)
        
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)
    
    def _create_welcome_section(self, layout: QVBoxLayout) -> None:
        """
        创建欢迎区域
        
        Args:
            layout: 父布局
        """
        welcome_widget = QWidget()
        welcome_layout = QVBoxLayout(welcome_widget)
        welcome_layout.setContentsMargins(0, 0, 0, 0)
        
        # 欢迎标题
        welcome_title = QLabel("欢迎使用 PySide6 开发框架")
        welcome_title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: 600;
                color: #1D1D1F;
                margin-bottom: 8px;
            }
        """)
        welcome_layout.addWidget(welcome_title)
        
        # 欢迎描述
        welcome_desc = QLabel("一个现代化、模块化的桌面应用程序开发框架")
        welcome_desc.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #8E8E93;
                margin-bottom: 16px;
            }
        """)
        welcome_layout.addWidget(welcome_desc)
        
        layout.addWidget(welcome_widget)
    
    def _create_stats_section(self, layout: QVBoxLayout) -> None:
        """
        创建统计区域
        
        Args:
            layout: 父布局
        """
        stats_group = QGroupBox("系统统计")
        stats_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: 600;
                color: #1D1D1F;
                border: 1px solid #E5E5EA;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                background-color: white;
            }
        """)
        
        stats_layout = QGridLayout(stats_group)
        stats_layout.setContentsMargins(16, 20, 16, 16)
        stats_layout.setSpacing(16)
        
        # 创建统计卡片
        stats_data = [
            ("总用户数", "1,234", "👥", 12.5),
            ("活跃用户", "856", "🔥", 8.3),
            ("今日访问", "2,456", "📊", -2.1),
            ("系统负载", "68%", "⚡", 5.7),
        ]
        
        for i, (title, value, icon, trend) in enumerate(stats_data):
            card = StatCard(title, value, icon, trend)
            self.stat_cards[title] = card
            
            row = i // 2
            col = i % 2
            stats_layout.addWidget(card, row, col)
        
        layout.addWidget(stats_group)
    
    def _create_charts_section(self, layout: QVBoxLayout) -> None:
        """
        创建图表区域
        
        Args:
            layout: 父布局
        """
        charts_group = QGroupBox("数据分析")
        charts_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: 600;
                color: #1D1D1F;
                border: 1px solid #E5E5EA;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                background-color: white;
            }
        """)
        
        charts_layout = QGridLayout(charts_group)
        charts_layout.setContentsMargins(16, 20, 16, 16)
        charts_layout.setSpacing(16)
        
        # 创建图表
        line_chart = ChartWidget("line")
        line_chart.setMinimumHeight(300)
        self.charts["line"] = line_chart
        charts_layout.addWidget(line_chart, 0, 0, 1, 2)
        
        pie_chart = ChartWidget("pie")
        pie_chart.setMinimumHeight(250)
        self.charts["pie"] = pie_chart
        charts_layout.addWidget(pie_chart, 1, 0)
        
        bar_chart = ChartWidget("bar")
        bar_chart.setMinimumHeight(250)
        self.charts["bar"] = bar_chart
        charts_layout.addWidget(bar_chart, 1, 1)
        
        layout.addWidget(charts_group)
    
    def _create_quick_actions_section(self, layout: QVBoxLayout) -> None:
        """
        创建快速操作区域
        
        Args:
            layout: 父布局
        """
        actions_group = QGroupBox("快速操作")
        actions_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: 600;
                color: #1D1D1F;
                border: 1px solid #E5E5EA;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                background-color: white;
            }
        """)
        
        actions_layout = QHBoxLayout(actions_group)
        actions_layout.setContentsMargins(16, 20, 16, 16)
        actions_layout.setSpacing(16)
        
        # 创建快速操作按钮
        actions_data = [
            ("🚀", "新建项目", "创建新项目", "new_project"),
            ("📊", "生成报告", "导出数据报告", "generate_report"),
            ("🔧", "系统设置", "配置系统参数", "system_settings"),
            ("📚", "查看文档", "打开帮助文档", "view_docs"),
            ("🔄", "数据同步", "同步远程数据", "sync_data"),
        ]
        
        for icon, text, desc, action_key in actions_data:
            button = QuickActionButton(icon, text, desc)
            button.clicked.connect(lambda checked, key=action_key: self.quick_action_clicked.emit(key))
            actions_layout.addWidget(button)
        
        actions_layout.addStretch()
        layout.addWidget(actions_group)
    
    def _update_data(self) -> None:
        """更新数据"""
        # 更新统计卡片
        for title, card in self.stat_cards.items():
            if title == "总用户数":
                new_value = f"{random.randint(1200, 1300):,}"
                new_trend = random.uniform(-5, 15)
            elif title == "活跃用户":
                new_value = f"{random.randint(800, 900):,}"
                new_trend = random.uniform(-3, 12)
            elif title == "今日访问":
                new_value = f"{random.randint(2000, 3000):,}"
                new_trend = random.uniform(-8, 10)
            elif title == "系统负载":
                new_value = f"{random.randint(50, 85)}%"
                new_trend = random.uniform(-10, 8)
            else:
                continue
            
            card.update_value(new_value, new_trend)
        
        self.logger.debug("仪表盘数据已更新")
    
    def on_theme_changed(self, theme_name: str) -> None:
        """
        主题变更处理
        
        Args:
            theme_name: 新主题名称
        """
        # 根据主题更新样式
        if theme_name == "dark":
            # 深色主题样式
            pass
        else:
            # 浅色主题样式
            pass
        
        self.logger.debug(f"仪表盘主题已更新: {theme_name}")
    
    def cleanup(self) -> None:
        """清理资源"""
        if self.update_timer.isActive():
            self.update_timer.stop()
        
        super().cleanup()