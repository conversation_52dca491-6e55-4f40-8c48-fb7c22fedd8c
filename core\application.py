"""
主应用程序类

提供应用程序的核心功能，包括：
- 应用程序初始化和配置
- 全局异常处理
- 主题管理
- 窗口管理
- 性能监控
"""

import sys
import os
import logging
import traceback
from typing import Optional, Dict, Any
from pathlib import Path

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QObject, Signal, QTimer, QThread, QSettings
from PySide6.QtGui import QIcon, QFont

from .theme_manager import ThemeManager


class Application(QApplication):
    """
    主应用程序类
    
    继承自QApplication，提供框架的核心功能和全局管理。
    """
    
    # 信号定义
    theme_changed = Signal(str)  # 主题变更信号
    error_occurred = Signal(str, str)  # 错误发生信号
    
    def __init__(self, argv: list):
        """
        初始化应用程序
        
        Args:
            argv: 命令行参数列表
        """
        super().__init__(argv)
        
        # 应用程序基本信息
        self.setApplicationName("PySide6开发框架")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("PySide6框架开发团队")
        self.setOrganizationDomain("pyside6-framework.com")
        
        # 初始化组件
        self._setup_logging()
        self._setup_exception_handling()
        self._setup_settings()
        self._setup_theme_manager()
        self._setup_fonts()
        self._setup_performance_monitor()
        
        # 主窗口引用
        self._main_window: Optional[QObject] = None
        
        # 应用程序状态
        self._is_initialized = False
        
        logging.info("应用程序初始化完成")
    
    def _setup_logging(self) -> None:
        """设置日志系统"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "application.log", encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("日志系统初始化完成")
    
    def _setup_exception_handling(self) -> None:
        """设置全局异常处理"""
        def handle_exception(exc_type, exc_value, exc_traceback):
            """全局异常处理器"""
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            
            error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            self.logger.error(f"未捕获的异常: {error_msg}")
            
            # 发送错误信号
            self.error_occurred.emit(str(exc_type.__name__), str(exc_value))
            
            # 显示错误对话框
            self._show_error_dialog(str(exc_type.__name__), str(exc_value))
        
        sys.excepthook = handle_exception
        self.logger.info("全局异常处理器设置完成")
    
    def _setup_settings(self) -> None:
        """设置应用程序配置"""
        self.settings = QSettings()
        
        # 设置默认值
        if not self.settings.contains("theme"):
            self.settings.setValue("theme", "light")
        if not self.settings.contains("font_size"):
            self.settings.setValue("font_size", 12)
        if not self.settings.contains("language"):
            self.settings.setValue("language", "zh_CN")
            
        self.logger.info("应用程序设置初始化完成")
    
    def _setup_theme_manager(self) -> None:
        """设置主题管理器"""
        self.theme_manager = ThemeManager(self)
        
        # 连接主题变更信号
        self.theme_manager.theme_changed.connect(self.theme_changed.emit)
        
        # 应用当前主题
        current_theme = self.settings.value("theme", "light")
        self.theme_manager.set_theme(current_theme)
        
        self.logger.info(f"主题管理器初始化完成，当前主题: {current_theme}")
    
    def _setup_fonts(self) -> None:
        """设置应用程序字体"""
        font_size = self.settings.value("font_size", 12, type=int)
        
        # 设置默认字体
        font = QFont()
        font.setFamily("Microsoft YaHei UI")  # Windows
        font.setPointSize(font_size)
        self.setFont(font)
        
        self.logger.info(f"应用程序字体设置完成，字体大小: {font_size}")
    
    def _setup_performance_monitor(self) -> None:
        """设置性能监控"""
        self.performance_timer = QTimer()
        self.performance_timer.timeout.connect(self._check_performance)
        self.performance_timer.start(30000)  # 每30秒检查一次
        
        self.logger.info("性能监控器启动完成")
    
    def _check_performance(self) -> None:
        """检查应用程序性能"""
        import psutil
        import os
        
        try:
            process = psutil.Process(os.getpid())
            memory_usage = process.memory_info().rss / 1024 / 1024  # MB
            cpu_usage = process.cpu_percent()
            
            self.logger.debug(f"性能监控 - 内存使用: {memory_usage:.2f}MB, CPU使用: {cpu_usage:.2f}%")
            
            # 如果内存使用超过200MB，记录警告
            if memory_usage > 200:
                self.logger.warning(f"内存使用过高: {memory_usage:.2f}MB")
                
        except Exception as e:
            self.logger.error(f"性能监控失败: {e}")
    
    def _show_error_dialog(self, error_type: str, error_message: str) -> None:
        """显示错误对话框"""
        try:
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle("应用程序错误")
            msg_box.setText(f"发生了一个未处理的错误：\n\n{error_type}: {error_message}")
            msg_box.setDetailedText(traceback.format_exc())
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.exec()
        except Exception as e:
            self.logger.error(f"显示错误对话框失败: {e}")
    
    def set_main_window(self, window: QObject) -> None:
        """
        设置主窗口
        
        Args:
            window: 主窗口对象
        """
        self._main_window = window
        self.logger.info("主窗口设置完成")
    
    def get_main_window(self) -> Optional[QObject]:
        """
        获取主窗口
        
        Returns:
            主窗口对象或None
        """
        return self._main_window
    
    def get_theme_manager(self) -> ThemeManager:
        """
        获取主题管理器
        
        Returns:
            主题管理器实例
        """
        return self.theme_manager
    
    def get_settings(self) -> QSettings:
        """
        获取应用程序设置
        
        Returns:
            QSettings实例
        """
        return self.settings
    
    def save_settings(self) -> None:
        """保存应用程序设置"""
        self.settings.sync()
        self.logger.info("应用程序设置已保存")
    
    def restart(self) -> None:
        """重启应用程序"""
        self.logger.info("正在重启应用程序...")
        self.quit()
        os.execl(sys.executable, sys.executable, *sys.argv)
    
    def shutdown(self) -> None:
        """优雅地关闭应用程序"""
        self.logger.info("正在关闭应用程序...")
        
        # 保存设置
        self.save_settings()
        
        # 停止性能监控
        if hasattr(self, 'performance_timer'):
            self.performance_timer.stop()
        
        # 关闭主窗口
        if self._main_window:
            self._main_window.close()
        
        # 退出应用程序
        self.quit()
        
        self.logger.info("应用程序已关闭")


def create_application(argv: list = None) -> Application:
    """
    创建应用程序实例
    
    Args:
        argv: 命令行参数，默认使用sys.argv
        
    Returns:
        Application实例
    """
    if argv is None:
        argv = sys.argv
    
    app = Application(argv)
    return app