"""
视图基类

提供所有视图的基础功能，包括：
- 统一的界面初始化
- 主题切换支持
- 响应式布局
- 动画效果
- 事件处理
"""

import logging
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, 
                               QLabel, QPushButton, QFrame)
from PySide6.QtCore import (QObject, Signal, QPropertyAnimation, 
                           QEasingCurve, QRect, QTimer, Qt)
from PySide6.QtGui import QFont, QPalette


class BaseView(QWidget):
    """
    视图基类
    
    提供所有视图的基础功能和统一接口。
    """
    
    # 信号定义
    view_initialized = Signal()  # 视图初始化完成信号
    view_destroyed = Signal()  # 视图销毁信号
    theme_applied = Signal(str)  # 主题应用信号
    animation_finished = Signal(str)  # 动画完成信号
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化视图基类
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 视图状态
        self._is_initialized = False
        self._current_theme = "light"
        self._animations: Dict[str, QPropertyAnimation] = {}
        
        # 布局管理
        self._main_layout: Optional[QVBoxLayout] = None
        self._header_widget: Optional[QWidget] = None
        self._content_widget: Optional[QWidget] = None
        self._footer_widget: Optional[QWidget] = None
        
        # 初始化视图
        self._setup_ui()
        self._setup_animations()
        self._connect_signals()
        
        self.logger.info(f"{self.__class__.__name__} 初始化完成")
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        # 设置基本属性
        self.setObjectName(self.__class__.__name__)
        
        # 创建主布局
        self._main_layout = QVBoxLayout(self)
        self._main_layout.setContentsMargins(0, 0, 0, 0)
        self._main_layout.setSpacing(0)
        
        # 创建头部区域
        self._header_widget = QWidget()
        self._header_widget.setObjectName("HeaderWidget")
        self._header_widget.setFixedHeight(0)  # 默认隐藏
        self._main_layout.addWidget(self._header_widget)
        
        # 创建内容区域
        self._content_widget = QWidget()
        self._content_widget.setObjectName("ContentWidget")
        self._main_layout.addWidget(self._content_widget, 1)
        
        # 创建底部区域
        self._footer_widget = QWidget()
        self._footer_widget.setObjectName("FooterWidget")
        self._footer_widget.setFixedHeight(0)  # 默认隐藏
        self._main_layout.addWidget(self._footer_widget)
        
        # 调用子类的UI设置方法
        self.setup_ui()
        
        self._is_initialized = True
        self.view_initialized.emit()
    
    def _setup_animations(self) -> None:
        """设置动画效果"""
        # 淡入淡出动画
        self._fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self._fade_animation.setDuration(300)
        self._fade_animation.setEasingCurve(QEasingCurve.InOutQuad)
        self._fade_animation.finished.connect(lambda: self.animation_finished.emit("fade"))
        
        # 滑动动画
        self._slide_animation = QPropertyAnimation(self, b"geometry")
        self._slide_animation.setDuration(300)
        self._slide_animation.setEasingCurve(QEasingCurve.OutCubic)
        self._slide_animation.finished.connect(lambda: self.animation_finished.emit("slide"))
        
        self._animations["fade"] = self._fade_animation
        self._animations["slide"] = self._slide_animation
    
    def _connect_signals(self) -> None:
        """连接信号和槽"""
        # 子类可以重写此方法来连接特定的信号
        pass
    
    @abstractmethod
    def setup_ui(self) -> None:
        """
        设置用户界面
        
        子类必须实现此方法来创建具体的界面元素。
        """
        pass
    
    def get_header_widget(self) -> QWidget:
        """
        获取头部组件
        
        Returns:
            头部组件
        """
        return self._header_widget
    
    def get_content_widget(self) -> QWidget:
        """
        获取内容组件
        
        Returns:
            内容组件
        """
        return self._content_widget
    
    def get_footer_widget(self) -> QWidget:
        """
        获取底部组件
        
        Returns:
            底部组件
        """
        return self._footer_widget
    
    def show_header(self, height: int = 50, animated: bool = True) -> None:
        """
        显示头部区域
        
        Args:
            height: 头部高度
            animated: 是否使用动画
        """
        if animated:
            self._animate_widget_height(self._header_widget, 0, height)
        else:
            self._header_widget.setFixedHeight(height)
    
    def hide_header(self, animated: bool = True) -> None:
        """
        隐藏头部区域
        
        Args:
            animated: 是否使用动画
        """
        if animated:
            current_height = self._header_widget.height()
            self._animate_widget_height(self._header_widget, current_height, 0)
        else:
            self._header_widget.setFixedHeight(0)
    
    def show_footer(self, height: int = 30, animated: bool = True) -> None:
        """
        显示底部区域
        
        Args:
            height: 底部高度
            animated: 是否使用动画
        """
        if animated:
            self._animate_widget_height(self._footer_widget, 0, height)
        else:
            self._footer_widget.setFixedHeight(height)
    
    def hide_footer(self, animated: bool = True) -> None:
        """
        隐藏底部区域
        
        Args:
            animated: 是否使用动画
        """
        if animated:
            current_height = self._footer_widget.height()
            self._animate_widget_height(self._footer_widget, current_height, 0)
        else:
            self._footer_widget.setFixedHeight(0)
    
    def _animate_widget_height(self, widget: QWidget, start_height: int, end_height: int) -> None:
        """
        动画改变组件高度
        
        Args:
            widget: 要动画的组件
            start_height: 起始高度
            end_height: 结束高度
        """
        animation = QPropertyAnimation(widget, b"maximumHeight")
        animation.setDuration(300)
        animation.setStartValue(start_height)
        animation.setEndValue(end_height)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        
        def on_finished():
            widget.setFixedHeight(end_height)
            animation.deleteLater()
        
        animation.finished.connect(on_finished)
        animation.start()
    
    def fade_in(self, duration: int = 300) -> None:
        """
        淡入动画
        
        Args:
            duration: 动画持续时间（毫秒）
        """
        self._fade_animation.setDuration(duration)
        self._fade_animation.setStartValue(0.0)
        self._fade_animation.setEndValue(1.0)
        self._fade_animation.start()
    
    def fade_out(self, duration: int = 300) -> None:
        """
        淡出动画
        
        Args:
            duration: 动画持续时间（毫秒）
        """
        self._fade_animation.setDuration(duration)
        self._fade_animation.setStartValue(1.0)
        self._fade_animation.setEndValue(0.0)
        self._fade_animation.start()
    
    def slide_in_from_left(self, duration: int = 300) -> None:
        """
        从左侧滑入
        
        Args:
            duration: 动画持续时间（毫秒）
        """
        start_rect = QRect(-self.width(), self.y(), self.width(), self.height())
        end_rect = self.geometry()
        
        self._slide_animation.setDuration(duration)
        self._slide_animation.setStartValue(start_rect)
        self._slide_animation.setEndValue(end_rect)
        self._slide_animation.start()
    
    def slide_in_from_right(self, duration: int = 300) -> None:
        """
        从右侧滑入
        
        Args:
            duration: 动画持续时间（毫秒）
        """
        parent_width = self.parent().width() if self.parent() else 1920
        start_rect = QRect(parent_width, self.y(), self.width(), self.height())
        end_rect = self.geometry()
        
        self._slide_animation.setDuration(duration)
        self._slide_animation.setStartValue(start_rect)
        self._slide_animation.setEndValue(end_rect)
        self._slide_animation.start()
    
    def apply_theme(self, theme_name: str) -> None:
        """
        应用主题
        
        Args:
            theme_name: 主题名称
        """
        self._current_theme = theme_name
        
        # 子类可以重写此方法来应用特定的主题样式
        self.on_theme_changed(theme_name)
        
        self.theme_applied.emit(theme_name)
        self.logger.info(f"主题已应用: {theme_name}")
    
    def on_theme_changed(self, theme_name: str) -> None:
        """
        主题变更处理
        
        子类可以重写此方法来处理主题变更。
        
        Args:
            theme_name: 新主题名称
        """
        pass
    
    def get_current_theme(self) -> str:
        """
        获取当前主题
        
        Returns:
            当前主题名称
        """
        return self._current_theme
    
    def is_initialized(self) -> bool:
        """
        检查视图是否已初始化
        
        Returns:
            是否已初始化
        """
        return self._is_initialized
    
    def set_loading(self, loading: bool, message: str = "加载中...") -> None:
        """
        设置加载状态
        
        Args:
            loading: 是否加载中
            message: 加载消息
        """
        # 子类可以重写此方法来显示加载状态
        if loading:
            self.setEnabled(False)
            self.logger.debug(f"开始加载: {message}")
        else:
            self.setEnabled(True)
            self.logger.debug("加载完成")
    
    def show_message(self, message: str, message_type: str = "info", duration: int = 3000) -> None:
        """
        显示消息
        
        Args:
            message: 消息内容
            message_type: 消息类型 (info, warning, error, success)
            duration: 显示持续时间（毫秒）
        """
        # 子类可以重写此方法来显示消息
        self.logger.info(f"消息 [{message_type}]: {message}")
    
    def cleanup(self) -> None:
        """清理视图资源"""
        # 停止所有动画
        for animation in self._animations.values():
            if animation.state() == QPropertyAnimation.Running:
                animation.stop()
        
        # 清理动画对象
        self._animations.clear()
        
        self.view_destroyed.emit()
        self.logger.info(f"{self.__class__.__name__} 清理完成")
    
    def closeEvent(self, event) -> None:
        """关闭事件处理"""
        self.cleanup()
        super().closeEvent(event)


class AnimatedWidget(QWidget):
    """
    带动画效果的组件基类
    
    提供常用的动画效果。
    """
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化动画组件
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        # 动画对象
        self._opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self._geometry_animation = QPropertyAnimation(self, b"geometry")
        
        # 设置动画属性
        self._opacity_animation.setDuration(200)
        self._opacity_animation.setEasingCurve(QEasingCurve.InOutQuad)
        
        self._geometry_animation.setDuration(200)
        self._geometry_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def animate_opacity(self, start_opacity: float, end_opacity: float, duration: int = 200) -> None:
        """
        透明度动画
        
        Args:
            start_opacity: 起始透明度
            end_opacity: 结束透明度
            duration: 动画持续时间
        """
        self._opacity_animation.setDuration(duration)
        self._opacity_animation.setStartValue(start_opacity)
        self._opacity_animation.setEndValue(end_opacity)
        self._opacity_animation.start()
    
    def animate_geometry(self, start_rect: QRect, end_rect: QRect, duration: int = 200) -> None:
        """
        几何形状动画
        
        Args:
            start_rect: 起始矩形
            end_rect: 结束矩形
            duration: 动画持续时间
        """
        self._geometry_animation.setDuration(duration)
        self._geometry_animation.setStartValue(start_rect)
        self._geometry_animation.setEndValue(end_rect)
        self._geometry_animation.start()
    
    def pulse_effect(self, scale_factor: float = 1.1, duration: int = 150) -> None:
        """
        脉冲效果
        
        Args:
            scale_factor: 缩放因子
            duration: 动画持续时间
        """
        original_rect = self.geometry()
        center = original_rect.center()
        
        # 计算缩放后的矩形
        scaled_width = int(original_rect.width() * scale_factor)
        scaled_height = int(original_rect.height() * scale_factor)
        scaled_rect = QRect(0, 0, scaled_width, scaled_height)
        scaled_rect.moveCenter(center)
        
        # 执行缩放动画
        self._geometry_animation.setDuration(duration)
        self._geometry_animation.setStartValue(original_rect)
        self._geometry_animation.setEndValue(scaled_rect)
        
        def return_to_original():
            self._geometry_animation.setStartValue(scaled_rect)
            self._geometry_animation.setEndValue(original_rect)
            self._geometry_animation.start()
        
        self._geometry_animation.finished.connect(return_to_original)
        self._geometry_animation.start()


class ResponsiveWidget(BaseView):
    """
    响应式组件
    
    根据窗口大小自动调整布局。
    """
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化响应式组件
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        # 断点定义
        self.breakpoints = {
            'xs': 480,   # 超小屏幕
            'sm': 768,   # 小屏幕
            'md': 1024,  # 中等屏幕
            'lg': 1280,  # 大屏幕
            'xl': 1920   # 超大屏幕
        }
        
        # 当前断点
        self._current_breakpoint = 'lg'
    
    def setup_ui(self) -> None:
        """设置响应式UI"""
        # 子类实现具体的UI设置
        pass
    
    def resizeEvent(self, event) -> None:
        """窗口大小变更事件"""
        super().resizeEvent(event)
        
        # 检查断点变化
        new_breakpoint = self._get_current_breakpoint()
        if new_breakpoint != self._current_breakpoint:
            self._current_breakpoint = new_breakpoint
            self._on_breakpoint_changed(new_breakpoint)
    
    def _get_current_breakpoint(self) -> str:
        """
        获取当前断点
        
        Returns:
            当前断点名称
        """
        width = self.width()
        
        if width < self.breakpoints['xs']:
            return 'xs'
        elif width < self.breakpoints['sm']:
            return 'sm'
        elif width < self.breakpoints['md']:
            return 'md'
        elif width < self.breakpoints['lg']:
            return 'lg'
        else:
            return 'xl'
    
    def _on_breakpoint_changed(self, breakpoint: str) -> None:
        """
        断点变化处理
        
        子类可以重写此方法来处理断点变化。
        
        Args:
            breakpoint: 新断点
        """
        self.logger.debug(f"断点变化: {self._current_breakpoint} -> {breakpoint}")
    
    def get_current_breakpoint(self) -> str:
        """
        获取当前断点
        
        Returns:
            当前断点名称
        """
        return self._current_breakpoint