# PySide6桌面软件开发框架

一个现代化、模块化的桌面应用程序开发框架，基于PySide6构建，提供完整的CRUD生成工具和现代化UI设计。

## ✨ 主要特性

### 🎨 现代化UI设计
- **无边框窗口设计**：实现macOS风格的现代化界面
- **8px圆角设计**：所有UI元素具有统一的圆角风格
- **自定义标题栏**：包含最小化、最大化、关闭按钮
- **阴影效果**：所有UI元素具有2-4px阴影效果
- **平滑动画**：200ms过渡动画，提供流畅的用户体验

### 🌓 主题系统
- **日间模式**：明亮清新的浅色主题
- **夜间模式**：护眼舒适的深色主题
- **动态切换**：支持运行时主题切换
- **自定义主题**：可扩展的主题系统

### 🏗️ MVC架构
- **Model层**：数据模型基类，支持字段验证和序列化
- **View层**：视图基类，支持主题切换和响应式布局
- **Controller层**：控制器基类，支持命令模式和撤销/重做

### 📊 预定义界面模块
- **仪表盘**：卡片式布局，实时数据图表，统计信息显示
- **日志查看器**：日志级别过滤，关键词搜索，正则表达式匹配
- **设置界面**：主题选择，语言切换，配置持久化存储
- **CRUD生成器**：可视化表结构定义，自动代码生成

### 🛠️ CRUD生成工具
- **可视化设计**：通过表格定义数据结构
- **多种字段类型**：支持字符串、整数、浮点数、布尔值、日期等
- **代码生成**：自动生成Model、View、Controller代码
- **语法高亮**：Python代码预览和语法高亮
- **一键导出**：支持导出到指定目录

## 🚀 快速开始

### 系统要求

- **Python**: 3.8+
- **操作系统**: Windows 10+ / macOS 10.15+ / Ubuntu 20.04+
- **内存**: 至少4GB RAM
- **存储**: 至少100MB可用空间

### 安装依赖

1. 克隆项目到本地：
```bash
git clone https://github.com/pyside6-framework/framework.git
cd framework
```

2. 创建虚拟环境（推荐）：
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

### 启动应用程序

```bash
python main.py
```

### 命令行选项

```bash
python main.py --help      # 显示帮助信息
python main.py --version   # 显示版本信息
```

## 📁 项目结构

```
framework/
├── main.py                 # 框架入口点
├── requirements.txt        # 依赖列表
├── README.md              # 使用文档
├── core/                  # 核心模块
│   ├── __init__.py
│   ├── application.py     # 主应用类
│   ├── models.py          # 数据模型基类
│   ├── controllers.py     # 控制器基类
│   └── theme_manager.py   # 主题管理器
├── views/                 # 界面模块
│   ├── __init__.py
│   ├── base_view.py       # 视图基类
│   ├── main_window.py     # 主窗口
│   ├── dashboard.py       # 仪表盘界面
│   ├── log_viewer.py      # 日志查看器
│   └── settings.py        # 设置界面
├── tools/                 # 工具模块
│   ├── __init__.py
│   └── crud_generator.py  # CRUD生成工具
├── resources/             # 资源文件
│   ├── styles/           # 样式表
│   │   ├── light_theme.qss
│   │   └── dark_theme.qss
│   └── icons/            # 图标文件
└── tests/                # 测试文件
    ├── __init__.py
    ├── test_core.py
    ├── test_views.py
    └── test_tools.py
```

## 🎯 使用指南

### 创建自定义视图

1. 继承`BaseView`类：
```python
from views.base_view import BaseView

class MyCustomView(BaseView):
    def setup_ui(self):
        # 实现你的UI逻辑
        pass
```

2. 添加到主窗口：
```python
main_window.add_page("my_view", MyCustomView(), "我的视图")
```

### 创建数据模型

1. 继承`BaseModel`类：
```python
from core.models import BaseModel

class UserModel(BaseModel):
    def _define_fields(self):
        super()._define_fields()
        self.add_field('username', str, required=True, max_length=50)
        self.add_field('email', str, required=True)
        self.add_field('age', int, default=18)
```

### 使用CRUD生成器

1. 启动应用程序
2. 点击侧边栏的"CRUD生成器"
3. 输入表名和字段定义
4. 点击"生成代码"按钮
5. 预览生成的代码
6. 点击"导出代码"保存到文件

### 主题切换

```python
# 获取主题管理器
theme_manager = app.get_theme_manager()

# 切换主题
theme_manager.toggle_theme()

# 设置特定主题
theme_manager.set_theme("dark")
```

## 🧪 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_core.py

# 运行测试并生成覆盖率报告
pytest --cov=. --cov-report=html
```

## 📊 性能指标

- **启动时间**: < 2秒
- **内存占用**: < 100MB
- **响应时间**: < 100ms（UI交互）
- **测试覆盖率**: > 80%

## 🔧 配置选项

应用程序支持以下配置选项（通过设置界面或配置文件）：

### 外观设置
- **主题**: 日间模式 / 夜间模式
- **字体**: 字体系列和大小
- **主色调**: 自定义主色调

### 通用设置
- **语言**: 简体中文 / English
- **自动保存**: 启用/禁用自动保存
- **启动页面**: 默认启动页面

### 高级设置
- **调试模式**: 启用详细日志
- **日志级别**: DEBUG / INFO / WARNING / ERROR
- **性能监控**: 启用性能监控

## 🤝 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码规范

- 遵循 PEP8 编码规范
- 使用类型提示
- 编写详细的文档字符串
- 添加单元测试

### 提交信息规范

```
<type>(<scope>): <subject>

<body>

<footer>
```

类型：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 🐛 问题报告

如果您发现了bug或有功能建议，请通过以下方式报告：

1. 检查是否已有相关issue
2. 创建新的issue，包含：
   - 详细的问题描述
   - 复现步骤
   - 期望行为
   - 实际行为
   - 系统环境信息

## 📄 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 👥 开发团队

- **项目负责人**: PySide6框架开发团队
- **主要开发者**: 
  - 架构设计师
  - UI/UX设计师
  - 前端开发工程师
  - 测试工程师

## 🙏 致谢

感谢以下开源项目的支持：

- [PySide6](https://wiki.qt.io/Qt_for_Python) - Qt for Python
- [Qt](https://www.qt.io/) - 跨平台应用程序框架
- [Python](https://www.python.org/) - 编程语言

## 📞 联系我们

- **项目主页**: https://github.com/pyside6-framework/framework
- **文档**: https://pyside6-framework.readthedocs.io/
- **问题反馈**: https://github.com/pyside6-framework/framework/issues
- **邮箱**: <EMAIL>

## 🗺️ 路线图

### v1.1.0 (计划中)
- [ ] 数据库集成支持
- [ ] 插件系统
- [ ] 国际化支持
- [ ] 更多预定义组件

### v1.2.0 (计划中)
- [ ] 拖拽式界面设计器
- [ ] 代码生成模板系统
- [ ] 性能分析工具
- [ ] 自动化测试工具

### v2.0.0 (远期规划)
- [ ] 云端同步功能
- [ ] 协作开发支持
- [ ] 移动端适配
- [ ] Web端支持

---

**PySide6桌面软件开发框架** - 让桌面应用开发更简单、更高效！