"""
主窗口

实现应用程序的主窗口，包括：
- 无边框窗口设计
- 自定义标题栏
- 可收缩的侧边栏
- 内容区域管理
- 窗口拖拽和调整大小
- 主题切换支持
"""

import logging
from typing import Optional, Dict, List
from pathlib import Path

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QFrame, QStackedWidget,
                               QScrollArea, QSizePolicy, QApplication)
from PySide6.QtCore import (Qt, Signal, QPropertyAnimation, QEasingCurve, 
                           QRect, QPoint, QSize, QTimer)
from PySide6.QtGui import (QFont, QIcon, QPalette, QPixmap, QPainter, 
                          QLinearGradient, QColor, QMouseEvent, QResizeEvent)

from .base_view import BaseView


class TitleBar(QWidget):
    """
    自定义标题栏
    
    实现macOS风格的标题栏，包含窗口控制按钮。
    """
    
    # 信号定义
    close_clicked = Signal()
    minimize_clicked = Signal()
    maximize_clicked = Signal()
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化标题栏
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        self.logger = logging.getLogger(__name__)
        
        # 拖拽相关
        self._drag_position = QPoint()
        self._is_dragging = False
        
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        self.setObjectName("TitleBar")
        self.setFixedHeight(44)
        
        # 主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 0, 12, 0)
        layout.setSpacing(8)
        
        # 窗口控制按钮
        self._create_control_buttons()
        layout.addWidget(self.control_buttons_widget)
        
        # 标题
        self.title_label = QLabel("PySide6开发框架")
        self.title_label.setObjectName("TitleLabel")
        self.title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.title_label, 1)
        
        # 右侧空白区域（用于对称）
        spacer_widget = QWidget()
        spacer_widget.setFixedWidth(54)  # 与控制按钮宽度相同
        layout.addWidget(spacer_widget)
    
    def _create_control_buttons(self) -> None:
        """创建窗口控制按钮"""
        self.control_buttons_widget = QWidget()
        control_layout = QHBoxLayout(self.control_buttons_widget)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(8)
        
        # 关闭按钮
        self.close_button = QPushButton()
        self.close_button.setObjectName("CloseButton")
        self.close_button.setFixedSize(12, 12)
        self.close_button.setToolTip("关闭")
        control_layout.addWidget(self.close_button)
        
        # 最小化按钮
        self.minimize_button = QPushButton()
        self.minimize_button.setObjectName("MinimizeButton")
        self.minimize_button.setFixedSize(12, 12)
        self.minimize_button.setToolTip("最小化")
        control_layout.addWidget(self.minimize_button)
        
        # 最大化按钮
        self.maximize_button = QPushButton()
        self.maximize_button.setObjectName("MaximizeButton")
        self.maximize_button.setFixedSize(12, 12)
        self.maximize_button.setToolTip("最大化")
        control_layout.addWidget(self.maximize_button)
    
    def _connect_signals(self) -> None:
        """连接信号和槽"""
        self.close_button.clicked.connect(self.close_clicked.emit)
        self.minimize_button.clicked.connect(self.minimize_clicked.emit)
        self.maximize_button.clicked.connect(self.maximize_clicked.emit)
    
    def set_title(self, title: str) -> None:
        """
        设置标题
        
        Args:
            title: 窗口标题
        """
        self.title_label.setText(title)
    
    def mousePressEvent(self, event: QMouseEvent) -> None:
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self._drag_position = event.globalPosition().toPoint() - self.window().frameGeometry().topLeft()
            self._is_dragging = True
            event.accept()
    
    def mouseMoveEvent(self, event: QMouseEvent) -> None:
        """鼠标移动事件"""
        if event.buttons() == Qt.LeftButton and self._is_dragging:
            self.window().move(event.globalPosition().toPoint() - self._drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event: QMouseEvent) -> None:
        """鼠标释放事件"""
        self._is_dragging = False
        event.accept()
    
    def mouseDoubleClickEvent(self, event: QMouseEvent) -> None:
        """鼠标双击事件"""
        if event.button() == Qt.LeftButton:
            self.maximize_clicked.emit()
            event.accept()


class SidebarButton(QPushButton):
    """
    侧边栏按钮
    
    带图标和文本的侧边栏导航按钮。
    """
    
    def __init__(self, icon: str, text: str, parent: Optional[QWidget] = None):
        """
        初始化侧边栏按钮
        
        Args:
            icon: 图标文本（可以是Unicode字符）
            text: 按钮文本
            parent: 父组件
        """
        super().__init__(parent)
        
        self.icon_text = icon
        self.button_text = text
        
        self.setObjectName("SidebarButton")
        self.setCheckable(True)
        self.setFixedHeight(44)
        
        self._setup_ui()
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(16, 8, 16, 8)
        layout.setSpacing(12)
        
        # 图标
        self.icon_label = QLabel(self.icon_text)
        self.icon_label.setObjectName("SidebarIcon")
        self.icon_label.setFixedSize(20, 20)
        self.icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.icon_label)
        
        # 文本
        self.text_label = QLabel(self.button_text)
        self.text_label.setObjectName("SidebarText")
        layout.addWidget(self.text_label, 1)
    
    def set_collapsed(self, collapsed: bool) -> None:
        """
        设置收缩状态
        
        Args:
            collapsed: 是否收缩
        """
        self.text_label.setVisible(not collapsed)
        if collapsed:
            self.setFixedWidth(48)
        else:
            self.setMinimumWidth(200)


class Sidebar(QWidget):
    """
    侧边栏
    
    可收缩的导航侧边栏。
    """
    
    # 信号定义
    item_selected = Signal(str)  # 项目选择信号
    collapsed_changed = Signal(bool)  # 收缩状态变更信号
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化侧边栏
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        self.logger = logging.getLogger(__name__)
        
        # 状态
        self._is_collapsed = False
        self._expanded_width = 240
        self._collapsed_width = 48
        
        # 按钮列表
        self._buttons: Dict[str, SidebarButton] = {}
        self._current_selection = None
        
        # 动画
        self._resize_animation = QPropertyAnimation(self, b"maximumWidth")
        self._resize_animation.setDuration(300)
        self._resize_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        self._setup_ui()
        self._create_navigation_items()
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        self.setObjectName("Sidebar")
        self.setFixedWidth(self._expanded_width)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 8, 0, 8)
        layout.setSpacing(2)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # 导航容器
        self.nav_widget = QWidget()
        self.nav_layout = QVBoxLayout(self.nav_widget)
        self.nav_layout.setContentsMargins(0, 0, 0, 0)
        self.nav_layout.setSpacing(2)
        
        scroll_area.setWidget(self.nav_widget)
        layout.addWidget(scroll_area)
        
        # 底部空白
        layout.addStretch()
        
        # 收缩/展开按钮
        self.toggle_button = QPushButton("◀")
        self.toggle_button.setObjectName("SidebarButton")
        self.toggle_button.setFixedHeight(32)
        self.toggle_button.setToolTip("收缩侧边栏")
        self.toggle_button.clicked.connect(self.toggle_collapsed)
        layout.addWidget(self.toggle_button)
    
    def _create_navigation_items(self) -> None:
        """创建导航项目"""
        nav_items = [
            ("📊", "仪表盘", "dashboard"),
            ("📝", "日志查看器", "log_viewer"),
            ("🛠️", "CRUD生成器", "crud_generator"),
            ("⚙️", "设置", "settings"),
        ]
        
        for icon, text, key in nav_items:
            self.add_navigation_item(key, icon, text)
    
    def add_navigation_item(self, key: str, icon: str, text: str) -> None:
        """
        添加导航项目
        
        Args:
            key: 项目键
            icon: 图标
            text: 文本
        """
        button = SidebarButton(icon, text)
        button.clicked.connect(lambda checked, k=key: self._on_item_clicked(k))
        
        self._buttons[key] = button
        self.nav_layout.addWidget(button)
        
        # 默认选择第一个项目
        if len(self._buttons) == 1:
            self.select_item(key)
    
    def _on_item_clicked(self, key: str) -> None:
        """
        处理项目点击
        
        Args:
            key: 项目键
        """
        self.select_item(key)
        self.item_selected.emit(key)
    
    def select_item(self, key: str) -> None:
        """
        选择项目
        
        Args:
            key: 项目键
        """
        if key not in self._buttons:
            return
        
        # 取消之前的选择
        if self._current_selection and self._current_selection in self._buttons:
            self._buttons[self._current_selection].setChecked(False)
        
        # 设置新选择
        self._buttons[key].setChecked(True)
        self._current_selection = key
        
        self.logger.debug(f"选择项目: {key}")
    
    def toggle_collapsed(self) -> None:
        """切换收缩状态"""
        self.set_collapsed(not self._is_collapsed)
    
    def set_collapsed(self, collapsed: bool) -> None:
        """
        设置收缩状态
        
        Args:
            collapsed: 是否收缩
        """
        if collapsed == self._is_collapsed:
            return
        
        self._is_collapsed = collapsed
        
        # 更新按钮状态
        for button in self._buttons.values():
            button.set_collapsed(collapsed)
        
        # 更新切换按钮
        if collapsed:
            self.toggle_button.setText("▶")
            self.toggle_button.setToolTip("展开侧边栏")
            target_width = self._collapsed_width
        else:
            self.toggle_button.setText("◀")
            self.toggle_button.setToolTip("收缩侧边栏")
            target_width = self._expanded_width
        
        # 执行动画
        self._resize_animation.setStartValue(self.width())
        self._resize_animation.setEndValue(target_width)
        self._resize_animation.finished.connect(lambda: self.setFixedWidth(target_width))
        self._resize_animation.start()
        
        self.collapsed_changed.emit(collapsed)
        self.logger.debug(f"侧边栏收缩状态: {collapsed}")
    
    def is_collapsed(self) -> bool:
        """
        检查是否收缩
        
        Returns:
            是否收缩
        """
        return self._is_collapsed
    
    def get_current_selection(self) -> Optional[str]:
        """
        获取当前选择
        
        Returns:
            当前选择的项目键
        """
        return self._current_selection


class MainWindow(QMainWindow):
    """
    主窗口
    
    应用程序的主窗口，实现无边框设计和现代化UI。
    """
    
    # 信号定义
    page_changed = Signal(str)  # 页面变更信号
    theme_toggle_requested = Signal()  # 主题切换请求信号
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        self.logger = logging.getLogger(__name__)
        
        # 窗口状态
        self._is_maximized = False
        self._normal_geometry = QRect()
        
        # 页面管理
        self._pages: Dict[str, QWidget] = {}
        
        self._setup_window()
        self._setup_ui()
        self._connect_signals()
        
        # 设置默认页面
        self._show_page("dashboard")
        
        self.logger.info("主窗口初始化完成")
    
    def _setup_window(self) -> None:
        """设置窗口属性"""
        # 无边框窗口
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowSystemMenuHint)
        self.setAttribute(Qt.WA_TranslucentBackground, False)
        
        # 窗口大小和位置
        self.setMinimumSize(800, 600)
        self.resize(1200, 800)
        
        # 居中显示
        self._center_window()
        
        # 窗口标题
        self.setWindowTitle("PySide6开发框架")
    
    def _center_window(self) -> None:
        """窗口居中"""
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.availableGeometry()
            window_geometry = self.frameGeometry()
            center_point = screen_geometry.center()
            window_geometry.moveCenter(center_point)
            self.move(window_geometry.topLeft())
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        # 主容器
        self.main_container = QWidget()
        self.main_container.setObjectName("MainContainer")
        self.setCentralWidget(self.main_container)
        
        # 主布局
        main_layout = QVBoxLayout(self.main_container)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 标题栏
        self.title_bar = TitleBar()
        main_layout.addWidget(self.title_bar)
        
        # 内容区域
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # 侧边栏
        self.sidebar = Sidebar()
        content_layout.addWidget(self.sidebar)
        
        # 主内容区域
        self.content_area = QWidget()
        self.content_area.setObjectName("ContentArea")
        content_layout.addWidget(self.content_area, 1)
        
        # 内容区域布局
        self.content_layout = QVBoxLayout(self.content_area)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(0)
        
        # 页面堆栈
        self.page_stack = QStackedWidget()
        self.content_layout.addWidget(self.page_stack)
        
        main_layout.addLayout(content_layout)
        
        # 创建页面
        self._create_pages()
    
    def _create_pages(self) -> None:
        """创建页面"""
        # 延迟导入以避免循环导入
        from .dashboard import Dashboard
        from .log_viewer import LogViewer
        from .settings import Settings
        from ..tools.crud_generator import CRUDGenerator
        
        # 创建页面实例
        pages = {
            "dashboard": Dashboard(),
            "log_viewer": LogViewer(),
            "crud_generator": CRUDGenerator(),
            "settings": Settings(),
        }
        
        # 添加到页面堆栈
        for key, page in pages.items():
            self._pages[key] = page
            self.page_stack.addWidget(page)
    
    def _connect_signals(self) -> None:
        """连接信号和槽"""
        # 标题栏信号
        self.title_bar.close_clicked.connect(self.close)
        self.title_bar.minimize_clicked.connect(self.showMinimized)
        self.title_bar.maximize_clicked.connect(self._toggle_maximize)
        
        # 侧边栏信号
        self.sidebar.item_selected.connect(self._on_sidebar_item_selected)
    
    def _on_sidebar_item_selected(self, key: str) -> None:
        """
        处理侧边栏项目选择
        
        Args:
            key: 选择的项目键
        """
        self._show_page(key)
    
    def _show_page(self, page_key: str) -> None:
        """
        显示页面
        
        Args:
            page_key: 页面键
        """
        if page_key not in self._pages:
            self.logger.warning(f"页面不存在: {page_key}")
            return
        
        page = self._pages[page_key]
        self.page_stack.setCurrentWidget(page)
        
        # 更新标题
        page_titles = {
            "dashboard": "仪表盘",
            "log_viewer": "日志查看器",
            "crud_generator": "CRUD生成器",
            "settings": "设置"
        }
        
        title = page_titles.get(page_key, "PySide6开发框架")
        self.title_bar.set_title(f"PySide6开发框架 - {title}")
        
        self.page_changed.emit(page_key)
        self.logger.debug(f"显示页面: {page_key}")
    
    def _toggle_maximize(self) -> None:
        """切换最大化状态"""
        if self._is_maximized:
            self._restore_window()
        else:
            self._maximize_window()
    
    def _maximize_window(self) -> None:
        """最大化窗口"""
        if not self._is_maximized:
            self._normal_geometry = self.geometry()
            
            screen = QApplication.primaryScreen()
            if screen:
                available_geometry = screen.availableGeometry()
                self.setGeometry(available_geometry)
            
            self._is_maximized = True
            self.title_bar.maximize_button.setToolTip("还原")
            self.logger.debug("窗口已最大化")
    
    def _restore_window(self) -> None:
        """还原窗口"""
        if self._is_maximized:
            self.setGeometry(self._normal_geometry)
            self._is_maximized = False
            self.title_bar.maximize_button.setToolTip("最大化")
            self.logger.debug("窗口已还原")
    
    def get_current_page(self) -> Optional[QWidget]:
        """
        获取当前页面
        
        Returns:
            当前页面组件
        """
        return self.page_stack.currentWidget()
    
    def get_page(self, page_key: str) -> Optional[QWidget]:
        """
        获取指定页面
        
        Args:
            page_key: 页面键
            
        Returns:
            页面组件或None
        """
        return self._pages.get(page_key)
    
    def add_page(self, page_key: str, page: QWidget, title: str = None) -> None:
        """
        添加页面
        
        Args:
            page_key: 页面键
            page: 页面组件
            title: 页面标题
        """
        if page_key in self._pages:
            self.logger.warning(f"页面已存在，将被替换: {page_key}")
            old_page = self._pages[page_key]
            self.page_stack.removeWidget(old_page)
        
        self._pages[page_key] = page
        self.page_stack.addWidget(page)
        
        self.logger.info(f"页面已添加: {page_key}")
    
    def remove_page(self, page_key: str) -> bool:
        """
        移除页面
        
        Args:
            page_key: 页面键
            
        Returns:
            是否移除成功
        """
        if page_key not in self._pages:
            return False
        
        page = self._pages[page_key]
        self.page_stack.removeWidget(page)
        del self._pages[page_key]
        
        self.logger.info(f"页面已移除: {page_key}")
        return True
    
    def apply_theme(self, theme_name: str) -> None:
        """
        应用主题
        
        Args:
            theme_name: 主题名称
        """
        # 应用主题到所有页面
        for page in self._pages.values():
            if hasattr(page, 'apply_theme'):
                page.apply_theme(theme_name)
        
        self.logger.info(f"主题已应用到主窗口: {theme_name}")
    
    def resizeEvent(self, event: QResizeEvent) -> None:
        """窗口大小变更事件"""
        super().resizeEvent(event)
        
        # 如果窗口不是最大化状态，保存当前几何形状
        if not self._is_maximized:
            self._normal_geometry = self.geometry()
    
    def closeEvent(self, event) -> None:
        """关闭事件处理"""
        # 清理所有页面
        for page in self._pages.values():
            if hasattr(page, 'cleanup'):
                page.cleanup()
        
        self.logger.info("主窗口正在关闭")
        super().closeEvent(event)