"""
视图模块测试

测试视图模块的功能，包括：
- BaseView类测试
- MainWindow类测试
- Dashboard类测试
- LogViewer类测试
- Settings类测试
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QWidget
from PySide6.QtCore import Qt, QTimer
from PySide6.QtTest import QTest

from views.base_view import BaseView, AnimatedWidget, ResponsiveWidget
from views.main_window import MainWindow, TitleBar, Sidebar
from views.dashboard import Dashboard, StatCard
from views.log_viewer import LogViewer, LogEntry, LogLevel
from views.settings import Settings, SettingItem


@pytest.fixture(scope="session")
def qapp():
    """创建QApplication实例"""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    yield app
    # 不要退出应用程序，因为可能有其他测试需要它


class TestBaseView:
    """BaseView类测试"""
    
    class TestView(BaseView):
        """测试视图类"""
        
        def setup_ui(self):
            # 简单的UI设置
            pass
    
    def test_base_view_creation(self, qapp):
        """测试基础视图创建"""
        view = self.TestView()
        
        assert view.is_initialized()
        assert view.get_current_theme() == "light"
        assert view.get_header_widget() is not None
        assert view.get_content_widget() is not None
        assert view.get_footer_widget() is not None
    
    def test_theme_application(self, qapp):
        """测试主题应用"""
        view = self.TestView()
        
        # 应用深色主题
        view.apply_theme("dark")
        assert view.get_current_theme() == "dark"
        
        # 应用浅色主题
        view.apply_theme("light")
        assert view.get_current_theme() == "light"
    
    def test_header_footer_visibility(self, qapp):
        """测试头部和底部可见性"""
        view = self.TestView()
        
        # 显示头部
        view.show_header(50, animated=False)
        assert view.get_header_widget().height() == 50
        
        # 隐藏头部
        view.hide_header(animated=False)
        assert view.get_header_widget().height() == 0
        
        # 显示底部
        view.show_footer(30, animated=False)
        assert view.get_footer_widget().height() == 30
        
        # 隐藏底部
        view.hide_footer(animated=False)
        assert view.get_footer_widget().height() == 0
    
    def test_loading_state(self, qapp):
        """测试加载状态"""
        view = self.TestView()
        
        # 设置加载状态
        view.set_loading(True, "加载中...")
        assert not view.isEnabled()
        
        # 取消加载状态
        view.set_loading(False)
        assert view.isEnabled()
    
    def test_cleanup(self, qapp):
        """测试清理"""
        view = self.TestView()
        view.cleanup()
        # 清理后应该没有异常


class TestAnimatedWidget:
    """AnimatedWidget类测试"""
    
    def test_animated_widget_creation(self, qapp):
        """测试动画组件创建"""
        widget = AnimatedWidget()
        assert widget is not None
    
    def test_opacity_animation(self, qapp):
        """测试透明度动画"""
        widget = AnimatedWidget()
        widget.show()
        
        # 开始透明度动画
        widget.animate_opacity(0.0, 1.0, 100)
        
        # 等待动画完成
        QTest.qWait(150)


class TestResponsiveWidget:
    """ResponsiveWidget类测试"""
    
    def test_responsive_widget_creation(self, qapp):
        """测试响应式组件创建"""
        widget = ResponsiveWidget()
        assert widget is not None
        assert widget.get_current_breakpoint() in ['xs', 'sm', 'md', 'lg', 'xl']
    
    def test_breakpoint_detection(self, qapp):
        """测试断点检测"""
        widget = ResponsiveWidget()
        widget.show()
        
        # 设置不同的窗口大小
        widget.resize(400, 300)
        QTest.qWait(10)
        # 小屏幕应该是xs或sm
        
        widget.resize(1200, 800)
        QTest.qWait(10)
        # 大屏幕应该是lg或xl


class TestTitleBar:
    """TitleBar类测试"""
    
    def test_title_bar_creation(self, qapp):
        """测试标题栏创建"""
        title_bar = TitleBar()
        
        assert title_bar.title_label.text() == "PySide6开发框架"
        assert title_bar.close_button is not None
        assert title_bar.minimize_button is not None
        assert title_bar.maximize_button is not None
    
    def test_title_setting(self, qapp):
        """测试标题设置"""
        title_bar = TitleBar()
        
        title_bar.set_title("测试标题")
        assert title_bar.title_label.text() == "测试标题"
    
    def test_button_signals(self, qapp):
        """测试按钮信号"""
        title_bar = TitleBar()
        
        # 模拟信号连接
        close_clicked = False
        minimize_clicked = False
        maximize_clicked = False
        
        def on_close():
            nonlocal close_clicked
            close_clicked = True
        
        def on_minimize():
            nonlocal minimize_clicked
            minimize_clicked = True
        
        def on_maximize():
            nonlocal maximize_clicked
            maximize_clicked = True
        
        title_bar.close_clicked.connect(on_close)
        title_bar.minimize_clicked.connect(on_minimize)
        title_bar.maximize_clicked.connect(on_maximize)
        
        # 模拟按钮点击
        title_bar.close_button.click()
        title_bar.minimize_button.click()
        title_bar.maximize_button.click()
        
        QTest.qWait(10)


class TestSidebar:
    """Sidebar类测试"""
    
    def test_sidebar_creation(self, qapp):
        """测试侧边栏创建"""
        sidebar = Sidebar()
        
        assert not sidebar.is_collapsed()
        assert sidebar.get_current_selection() is not None
    
    def test_sidebar_collapse(self, qapp):
        """测试侧边栏收缩"""
        sidebar = Sidebar()
        sidebar.show()
        
        # 收缩侧边栏
        sidebar.set_collapsed(True)
        assert sidebar.is_collapsed()
        
        # 展开侧边栏
        sidebar.set_collapsed(False)
        assert not sidebar.is_collapsed()
    
    def test_navigation_selection(self, qapp):
        """测试导航选择"""
        sidebar = Sidebar()
        
        # 选择项目
        sidebar.select_item("dashboard")
        assert sidebar.get_current_selection() == "dashboard"
        
        sidebar.select_item("settings")
        assert sidebar.get_current_selection() == "settings"


class TestMainWindow:
    """MainWindow类测试"""
    
    def test_main_window_creation(self, qapp):
        """测试主窗口创建"""
        # 使用mock来避免创建实际的页面
        with patch('views.main_window.Dashboard'), \
             patch('views.main_window.LogViewer'), \
             patch('views.main_window.Settings'), \
             patch('views.main_window.CRUDGenerator'):
            
            main_window = MainWindow()
            
            assert main_window.windowTitle() == "PySide6开发框架"
            assert main_window.title_bar is not None
            assert main_window.sidebar is not None
            assert main_window.page_stack is not None
    
    def test_page_management(self, qapp):
        """测试页面管理"""
        with patch('views.main_window.Dashboard'), \
             patch('views.main_window.LogViewer'), \
             patch('views.main_window.Settings'), \
             patch('views.main_window.CRUDGenerator'):
            
            main_window = MainWindow()
            
            # 添加测试页面
            test_page = QWidget()
            main_window.add_page("test_page", test_page, "测试页面")
            
            # 获取页面
            retrieved_page = main_window.get_page("test_page")
            assert retrieved_page == test_page
            
            # 移除页面
            success = main_window.remove_page("test_page")
            assert success
            assert main_window.get_page("test_page") is None
    
    def test_window_maximize_restore(self, qapp):
        """测试窗口最大化和还原"""
        with patch('views.main_window.Dashboard'), \
             patch('views.main_window.LogViewer'), \
             patch('views.main_window.Settings'), \
             patch('views.main_window.CRUDGenerator'):
            
            main_window = MainWindow()
            main_window.show()
            
            original_geometry = main_window.geometry()
            
            # 最大化
            main_window._maximize_window()
            assert main_window._is_maximized
            
            # 还原
            main_window._restore_window()
            assert not main_window._is_maximized


class TestStatCard:
    """StatCard类测试"""
    
    def test_stat_card_creation(self, qapp):
        """测试统计卡片创建"""
        card = StatCard("用户数", "1,234", "👥", 12.5)
        
        assert card.title == "用户数"
        assert card.current_value == "1,234"
        assert card.icon == "👥"
        assert card.trend == 12.5
    
    def test_stat_card_update(self, qapp):
        """测试统计卡片更新"""
        card = StatCard("用户数", "1,000", "👥", 10.0)
        
        # 更新值
        card.update_value("1,500", 15.0)
        assert card.current_value == "1,500"
        assert card.trend == 15.0


class TestDashboard:
    """Dashboard类测试"""
    
    def test_dashboard_creation(self, qapp):
        """测试仪表盘创建"""
        dashboard = Dashboard()
        
        assert dashboard.is_initialized()
        assert len(dashboard.stat_cards) > 0
        assert len(dashboard.charts) > 0
    
    def test_dashboard_data_update(self, qapp):
        """测试仪表盘数据更新"""
        dashboard = Dashboard()
        
        # 模拟数据更新
        dashboard._update_data()
        
        # 检查统计卡片是否有数据
        for card in dashboard.stat_cards.values():
            assert card.current_value != ""
    
    def test_dashboard_cleanup(self, qapp):
        """测试仪表盘清理"""
        dashboard = Dashboard()
        dashboard.cleanup()
        
        # 确保定时器已停止
        assert not dashboard.update_timer.isActive()


class TestLogEntry:
    """LogEntry类测试"""
    
    def test_log_entry_creation(self):
        """测试日志条目创建"""
        from datetime import datetime
        
        timestamp = datetime.now()
        entry = LogEntry(timestamp, LogLevel.INFO, "TestLogger", "Test message")
        
        assert entry.timestamp == timestamp
        assert entry.level == LogLevel.INFO
        assert entry.logger == "TestLogger"
        assert entry.message == "Test message"
    
    def test_log_entry_serialization(self):
        """测试日志条目序列化"""
        from datetime import datetime
        
        timestamp = datetime.now()
        entry = LogEntry(timestamp, LogLevel.ERROR, "TestLogger", "Error message")
        
        # 转换为字典
        data = entry.to_dict()
        assert data["level"] == LogLevel.ERROR
        assert data["logger"] == "TestLogger"
        assert data["message"] == "Error message"
        
        # 从字典创建
        new_entry = LogEntry.from_dict(data)
        assert new_entry.level == entry.level
        assert new_entry.logger == entry.logger
        assert new_entry.message == entry.message


class TestLogViewer:
    """LogViewer类测试"""
    
    def test_log_viewer_creation(self, qapp):
        """测试日志查看器创建"""
        log_viewer = LogViewer()
        
        assert log_viewer.is_initialized()
        assert len(log_viewer.log_entries) > 0  # 应该有示例日志
        assert log_viewer.search_text == ""
        assert not log_viewer.use_regex
    
    def test_log_filtering(self, qapp):
        """测试日志过滤"""
        log_viewer = LogViewer()
        
        # 添加测试日志
        from datetime import datetime
        test_entry = LogEntry(datetime.now(), LogLevel.DEBUG, "Test", "Debug message")
        log_viewer.add_log_entry(test_entry)
        
        # 测试级别过滤
        log_viewer._on_level_filter_changed(LogLevel.DEBUG, False)
        log_viewer._apply_filters()
        
        # DEBUG级别的日志应该被过滤掉
        debug_entries = [e for e in log_viewer.filtered_entries if e.level == LogLevel.DEBUG]
        assert len(debug_entries) == 0
    
    def test_log_search(self, qapp):
        """测试日志搜索"""
        log_viewer = LogViewer()
        
        # 搜索测试
        log_viewer._on_search_changed("应用程序")
        
        # 应该找到包含"应用程序"的日志
        matching_entries = [e for e in log_viewer.filtered_entries 
                          if "应用程序" in e.message]
        assert len(matching_entries) > 0
    
    def test_log_export(self, qapp):
        """测试日志导出"""
        log_viewer = LogViewer()
        
        # 模拟导出（不实际创建文件）
        with patch('PySide6.QtWidgets.QFileDialog.getSaveFileName') as mock_dialog:
            mock_dialog.return_value = ("test.txt", "TXT文件 (*.txt)")
            
            with patch('builtins.open', create=True) as mock_open:
                mock_file = MagicMock()
                mock_open.return_value.__enter__.return_value = mock_file
                
                log_viewer._export_to_txt()
                
                # 验证文件写入被调用
                mock_file.write.assert_called()


class TestSettingItem:
    """SettingItem类测试"""
    
    def test_text_setting_item(self, qapp):
        """测试文本设置项"""
        item = SettingItem("test_key", "测试设置", "这是一个测试设置", 
                          "text", default_value="默认值")
        
        assert item.key == "test_key"
        assert item.title == "测试设置"
        assert item.get_value() == "默认值"
        
        # 设置新值
        item.set_value("新值")
        assert item.get_value() == "新值"
    
    def test_combo_setting_item(self, qapp):
        """测试下拉框设置项"""
        options = [("选项1", "value1"), ("选项2", "value2")]
        item = SettingItem("combo_key", "下拉设置", "", "combo", 
                          options=options, default_value="value1")
        
        assert item.get_value() == "value1"
        
        # 设置新值
        item.set_value("value2")
        assert item.get_value() == "value2"
    
    def test_checkbox_setting_item(self, qapp):
        """测试复选框设置项"""
        item = SettingItem("check_key", "复选框设置", "", "checkbox", 
                          default_value=True)
        
        assert item.get_value() is True
        
        # 设置新值
        item.set_value(False)
        assert item.get_value() is False


class TestSettings:
    """Settings类测试"""
    
    def test_settings_creation(self, qapp):
        """测试设置界面创建"""
        settings = Settings()
        
        assert settings.is_initialized()
        assert len(settings.setting_items) > 0
        assert settings.tab_widget.count() > 0
    
    def test_setting_value_management(self, qapp):
        """测试设置值管理"""
        settings = Settings()
        
        # 设置值
        settings.set_setting_value("test_key", "test_value")
        assert settings.get_setting_value("test_key") == "test_value"
        
        # 获取不存在的设置
        value = settings.get_setting_value("nonexistent", "default")
        assert value == "default"
    
    def test_settings_export_import(self, qapp):
        """测试设置导出导入"""
        settings = Settings()
        
        # 模拟导出
        with patch('PySide6.QtWidgets.QFileDialog.getSaveFileName') as mock_save:
            mock_save.return_value = ("settings.json", "JSON文件 (*.json)")
            
            with patch('builtins.open', create=True) as mock_open:
                mock_file = MagicMock()
                mock_open.return_value.__enter__.return_value = mock_file
                
                settings._export_settings()
                
                # 验证文件写入被调用
                mock_file.write.assert_called()
        
        # 模拟导入
        with patch('PySide6.QtWidgets.QFileDialog.getOpenFileName') as mock_open_dialog:
            mock_open_dialog.return_value = ("settings.json", "JSON文件 (*.json)")
            
            with patch('builtins.open', create=True) as mock_open:
                mock_file = MagicMock()
                mock_file.read.return_value = '{"theme": "dark", "font_size": 14}'
                mock_open.return_value.__enter__.return_value = mock_file
                
                settings._import_settings()
                
                # 验证文件读取被调用
                mock_file.read.assert_called()


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])