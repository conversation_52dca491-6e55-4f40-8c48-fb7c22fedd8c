"""
日志查看器

实现日志查看和管理功能，包括：
- 日志级别过滤
- 关键词搜索
- 正则表达式匹配
- 日志导出
- 实时日志监控
"""

import logging
import re
import json
import csv
from typing import List, Dict, Optional, Any
from datetime import datetime
from pathlib import Path

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QPushButton, QLineEdit, QComboBox,
                               QTableWidget, QTableWidgetItem, QTextEdit,
                               QCheckBox, QGroupBox, QSplitter, QHeaderView,
                               QFileDialog, QMessageBox, QProgressBar)
from PySide6.QtCore import Qt, Signal, QTimer, QThread, pyqtSignal
from PySide6.QtGui import QFont, QColor, QTextCharFormat, QSyntaxHighlighter

from .base_view import BaseView


class LogLevel:
    """日志级别常量"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"
    
    ALL_LEVELS = [DEBUG, INFO, WARNING, ERROR, CRITICAL]
    
    COLORS = {
        DEBUG: "#8E8E93",
        INFO: "#007AFF",
        WARNING: "#FF9500",
        ERROR: "#FF3B30",
        CRITICAL: "#D70015"
    }


class LogEntry:
    """日志条目"""
    
    def __init__(self, timestamp: datetime, level: str, logger: str, message: str):
        """
        初始化日志条目
        
        Args:
            timestamp: 时间戳
            level: 日志级别
            logger: 记录器名称
            message: 日志消息
        """
        self.timestamp = timestamp
        self.level = level
        self.logger = logger
        self.message = message
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "level": self.level,
            "logger": self.logger,
            "message": self.message
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LogEntry':
        """从字典创建"""
        timestamp = datetime.fromisoformat(data["timestamp"])
        return cls(timestamp, data["level"], data["logger"], data["message"])


class LogHighlighter(QSyntaxHighlighter):
    """
    日志语法高亮器
    
    为不同级别的日志提供颜色高亮。
    """
    
    def __init__(self, parent=None):
        """初始化高亮器"""
        super().__init__(parent)
        
        # 定义高亮规则
        self.highlighting_rules = []
        
        # 日志级别高亮
        for level, color in LogLevel.COLORS.items():
            format = QTextCharFormat()
            format.setForeground(QColor(color))
            format.setFontWeight(QFont.Bold)
            pattern = f"\\b{level}\\b"
            self.highlighting_rules.append((re.compile(pattern), format))
        
        # 时间戳高亮
        timestamp_format = QTextCharFormat()
        timestamp_format.setForeground(QColor("#8E8E93"))
        timestamp_pattern = r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}"
        self.highlighting_rules.append((re.compile(timestamp_pattern), timestamp_format))
        
        # 错误关键词高亮
        error_format = QTextCharFormat()
        error_format.setForeground(QColor("#FF3B30"))
        error_format.setFontWeight(QFont.Bold)
        error_keywords = ["error", "exception", "failed", "failure", "crash"]
        for keyword in error_keywords:
            pattern = f"\\b{keyword}\\b"
            self.highlighting_rules.append((re.compile(pattern, re.IGNORECASE), error_format))
    
    def highlightBlock(self, text: str) -> None:
        """高亮文本块"""
        for pattern, format in self.highlighting_rules:
            for match in pattern.finditer(text):
                start, end = match.span()
                self.setFormat(start, end - start, format)


class LogFileWatcher(QThread):
    """
    日志文件监控器
    
    监控日志文件变化并发送新日志条目。
    """
    
    new_log_entry = pyqtSignal(LogEntry)
    
    def __init__(self, log_file_path: str):
        """
        初始化日志文件监控器
        
        Args:
            log_file_path: 日志文件路径
        """
        super().__init__()
        
        self.log_file_path = Path(log_file_path)
        self.last_position = 0
        self.running = False
    
    def run(self) -> None:
        """运行监控"""
        self.running = True
        
        while self.running:
            try:
                if self.log_file_path.exists():
                    with open(self.log_file_path, 'r', encoding='utf-8') as f:
                        f.seek(self.last_position)
                        new_lines = f.readlines()
                        self.last_position = f.tell()
                        
                        for line in new_lines:
                            log_entry = self._parse_log_line(line.strip())
                            if log_entry:
                                self.new_log_entry.emit(log_entry)
                
                self.msleep(1000)  # 每秒检查一次
                
            except Exception as e:
                logging.error(f"日志文件监控错误: {e}")
                self.msleep(5000)  # 出错时等待5秒
    
    def stop(self) -> None:
        """停止监控"""
        self.running = False
        self.wait()
    
    def _parse_log_line(self, line: str) -> Optional[LogEntry]:
        """
        解析日志行
        
        Args:
            line: 日志行
            
        Returns:
            日志条目或None
        """
        # 简单的日志格式解析
        # 格式: 2023-12-01 10:30:45 - logger_name - LEVEL - message
        pattern = r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - (.*?) - (.*?) - (.*)"
        match = re.match(pattern, line)
        
        if match:
            timestamp_str, logger, level, message = match.groups()
            try:
                timestamp = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
                return LogEntry(timestamp, level, logger, message)
            except ValueError:
                pass
        
        return None


class LogViewer(BaseView):
    """
    日志查看器
    
    提供完整的日志查看和管理功能。
    """
    
    # 信号定义
    log_exported = Signal(str, str)  # 日志导出信号 (格式, 文件路径)
    
    def __init__(self, parent: QWidget = None):
        """
        初始化日志查看器
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        # 日志数据
        self.log_entries: List[LogEntry] = []
        self.filtered_entries: List[LogEntry] = []
        
        # 过滤器状态
        self.level_filters = {level: True for level in LogLevel.ALL_LEVELS}
        self.search_text = ""
        self.use_regex = False
        
        # 文件监控
        self.file_watcher: Optional[LogFileWatcher] = None
        
        # 自动刷新定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self._refresh_logs)
        
        # 加载示例日志
        self._load_sample_logs()
    
    def setup_ui(self) -> None:
        """设置用户界面"""
        content_widget = self.get_content_widget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 工具栏
        self._create_toolbar(layout)
        
        # 主分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：过滤器面板
        self._create_filter_panel(splitter)
        
        # 右侧：日志显示区域
        self._create_log_display(splitter)
        
        # 设置分割器比例
        splitter.setSizes([250, 750])
        layout.addWidget(splitter)
        
        # 状态栏
        self._create_status_bar(layout)
    
    def _create_toolbar(self, layout: QVBoxLayout) -> None:
        """
        创建工具栏
        
        Args:
            layout: 父布局
        """
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)
        toolbar_layout.setSpacing(12)
        
        # 搜索框
        search_label = QLabel("搜索:")
        toolbar_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入关键词或正则表达式...")
        self.search_input.textChanged.connect(self._on_search_changed)
        toolbar_layout.addWidget(self.search_input, 1)
        
        # 正则表达式复选框
        self.regex_checkbox = QCheckBox("正则表达式")
        self.regex_checkbox.toggled.connect(self._on_regex_toggled)
        toolbar_layout.addWidget(self.regex_checkbox)
        
        # 分隔符
        toolbar_layout.addWidget(QLabel("|"))
        
        # 刷新按钮
        refresh_button = QPushButton("🔄 刷新")
        refresh_button.clicked.connect(self._refresh_logs)
        toolbar_layout.addWidget(refresh_button)
        
        # 清空按钮
        clear_button = QPushButton("🗑️ 清空")
        clear_button.clicked.connect(self._clear_logs)
        toolbar_layout.addWidget(clear_button)
        
        # 导出按钮
        export_button = QPushButton("📤 导出")
        export_button.clicked.connect(self._export_logs)
        toolbar_layout.addWidget(export_button)
        
        # 设置按钮
        settings_button = QPushButton("⚙️ 设置")
        settings_button.clicked.connect(self._show_settings)
        toolbar_layout.addWidget(settings_button)
        
        layout.addWidget(toolbar_widget)
    
    def _create_filter_panel(self, splitter: QSplitter) -> None:
        """
        创建过滤器面板
        
        Args:
            splitter: 分割器
        """
        filter_widget = QWidget()
        filter_layout = QVBoxLayout(filter_widget)
        filter_layout.setContentsMargins(0, 0, 0, 0)
        filter_layout.setSpacing(16)
        
        # 日志级别过滤
        level_group = QGroupBox("日志级别")
        level_layout = QVBoxLayout(level_group)
        
        self.level_checkboxes = {}
        for level in LogLevel.ALL_LEVELS:
            checkbox = QCheckBox(level)
            checkbox.setChecked(True)
            checkbox.toggled.connect(lambda checked, l=level: self._on_level_filter_changed(l, checked))
            
            # 设置颜色
            color = LogLevel.COLORS.get(level, "#000000")
            checkbox.setStyleSheet(f"QCheckBox {{ color: {color}; font-weight: bold; }}")
            
            self.level_checkboxes[level] = checkbox
            level_layout.addWidget(checkbox)
        
        filter_layout.addWidget(level_group)
        
        # 时间范围过滤
        time_group = QGroupBox("时间范围")
        time_layout = QVBoxLayout(time_group)
        
        # 预设时间范围
        time_ranges = [
            ("最近1小时", 1),
            ("最近6小时", 6),
            ("最近24小时", 24),
            ("最近7天", 24 * 7),
            ("全部", -1)
        ]
        
        self.time_combo = QComboBox()
        for name, hours in time_ranges:
            self.time_combo.addItem(name, hours)
        
        self.time_combo.setCurrentIndex(4)  # 默认选择"全部"
        self.time_combo.currentIndexChanged.connect(self._on_time_range_changed)
        time_layout.addWidget(self.time_combo)
        
        filter_layout.addWidget(time_group)
        
        # 记录器过滤
        logger_group = QGroupBox("记录器")
        logger_layout = QVBoxLayout(logger_group)
        
        self.logger_combo = QComboBox()
        self.logger_combo.addItem("全部记录器", "")
        self.logger_combo.currentTextChanged.connect(self._apply_filters)
        logger_layout.addWidget(self.logger_combo)
        
        filter_layout.addWidget(logger_group)
        
        # 统计信息
        stats_group = QGroupBox("统计信息")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_label = QLabel("总计: 0 条日志")
        stats_layout.addWidget(self.stats_label)
        
        filter_layout.addWidget(stats_group)
        
        filter_layout.addStretch()
        splitter.addWidget(filter_widget)
    
    def _create_log_display(self, splitter: QSplitter) -> None:
        """
        创建日志显示区域
        
        Args:
            splitter: 分割器
        """
        display_widget = QWidget()
        display_layout = QVBoxLayout(display_widget)
        display_layout.setContentsMargins(0, 0, 0, 0)
        display_layout.setSpacing(8)
        
        # 显示模式选择
        mode_widget = QWidget()
        mode_layout = QHBoxLayout(mode_widget)
        mode_layout.setContentsMargins(0, 0, 0, 0)
        
        mode_label = QLabel("显示模式:")
        mode_layout.addWidget(mode_label)
        
        self.display_mode_combo = QComboBox()
        self.display_mode_combo.addItems(["表格视图", "文本视图"])
        self.display_mode_combo.currentTextChanged.connect(self._on_display_mode_changed)
        mode_layout.addWidget(self.display_mode_combo)
        
        mode_layout.addStretch()
        display_layout.addWidget(mode_widget)
        
        # 日志显示区域（使用堆栈布局切换视图）
        from PySide6.QtWidgets import QStackedWidget
        self.display_stack = QStackedWidget()
        
        # 表格视图
        self._create_table_view()
        self.display_stack.addWidget(self.log_table)
        
        # 文本视图
        self._create_text_view()
        self.display_stack.addWidget(self.log_text)
        
        display_layout.addWidget(self.display_stack)
        splitter.addWidget(display_widget)
    
    def _create_table_view(self) -> None:
        """创建表格视图"""
        self.log_table = QTableWidget()
        self.log_table.setColumnCount(4)
        self.log_table.setHorizontalHeaderLabels(["时间", "级别", "记录器", "消息"])
        
        # 设置列宽
        header = self.log_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 时间
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 级别
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 记录器
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # 消息
        
        # 设置表格属性
        self.log_table.setAlternatingRowColors(True)
        self.log_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.log_table.setSortingEnabled(True)
    
    def _create_text_view(self) -> None:
        """创建文本视图"""
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 10))
        
        # 设置语法高亮
        self.highlighter = LogHighlighter(self.log_text.document())
    
    def _create_status_bar(self, layout: QVBoxLayout) -> None:
        """
        创建状态栏
        
        Args:
            layout: 父布局
        """
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(0, 0, 0, 0)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_layout.addWidget(self.progress_bar)
        
        # 自动刷新状态
        self.auto_refresh_checkbox = QCheckBox("自动刷新")
        self.auto_refresh_checkbox.toggled.connect(self._on_auto_refresh_toggled)
        status_layout.addWidget(self.auto_refresh_checkbox)
        
        layout.addWidget(status_widget)
    
    def _load_sample_logs(self) -> None:
        """加载示例日志"""
        sample_logs = [
            LogEntry(datetime.now(), LogLevel.INFO, "Application", "应用程序启动"),
            LogEntry(datetime.now(), LogLevel.DEBUG, "Database", "连接数据库成功"),
            LogEntry(datetime.now(), LogLevel.WARNING, "Network", "网络连接不稳定"),
            LogEntry(datetime.now(), LogLevel.ERROR, "FileSystem", "文件读取失败"),
            LogEntry(datetime.now(), LogLevel.INFO, "UserInterface", "用户界面初始化完成"),
            LogEntry(datetime.now(), LogLevel.CRITICAL, "Security", "检测到安全威胁"),
        ]
        
        self.log_entries.extend(sample_logs)
        self._apply_filters()
        self._update_logger_combo()
    
    def _on_search_changed(self, text: str) -> None:
        """
        搜索文本变更处理
        
        Args:
            text: 搜索文本
        """
        self.search_text = text
        self._apply_filters()
    
    def _on_regex_toggled(self, checked: bool) -> None:
        """
        正则表达式切换处理
        
        Args:
            checked: 是否启用正则表达式
        """
        self.use_regex = checked
        self._apply_filters()
    
    def _on_level_filter_changed(self, level: str, checked: bool) -> None:
        """
        日志级别过滤变更处理
        
        Args:
            level: 日志级别
            checked: 是否选中
        """
        self.level_filters[level] = checked
        self._apply_filters()
    
    def _on_time_range_changed(self, index: int) -> None:
        """
        时间范围变更处理
        
        Args:
            index: 选择的索引
        """
        self._apply_filters()
    
    def _on_display_mode_changed(self, mode: str) -> None:
        """
        显示模式变更处理
        
        Args:
            mode: 显示模式
        """
        if mode == "表格视图":
            self.display_stack.setCurrentWidget(self.log_table)
        else:
            self.display_stack.setCurrentWidget(self.log_text)
        
        self._update_display()
    
    def _on_auto_refresh_toggled(self, checked: bool) -> None:
        """
        自动刷新切换处理
        
        Args:
            checked: 是否启用自动刷新
        """
        if checked:
            self.refresh_timer.start(5000)  # 每5秒刷新
        else:
            self.refresh_timer.stop()
    
    def _apply_filters(self) -> None:
        """应用过滤器"""
        self.filtered_entries.clear()
        
        # 获取时间范围
        time_range_hours = self.time_combo.currentData()
        if time_range_hours > 0:
            cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
        else:
            cutoff_time = None
        
        # 获取记录器过滤
        selected_logger = self.logger_combo.currentData()
        
        for entry in self.log_entries:
            # 级别过滤
            if not self.level_filters.get(entry.level, True):
                continue
            
            # 时间过滤
            if cutoff_time and entry.timestamp < cutoff_time:
                continue
            
            # 记录器过滤
            if selected_logger and entry.logger != selected_logger:
                continue
            
            # 搜索过滤
            if self.search_text:
                if self.use_regex:
                    try:
                        pattern = re.compile(self.search_text, re.IGNORECASE)
                        if not pattern.search(entry.message):
                            continue
                    except re.error:
                        # 正则表达式错误，跳过过滤
                        pass
                else:
                    if self.search_text.lower() not in entry.message.lower():
                        continue
            
            self.filtered_entries.append(entry)
        
        self._update_display()
        self._update_stats()
    
    def _update_display(self) -> None:
        """更新显示"""
        current_widget = self.display_stack.currentWidget()
        
        if current_widget == self.log_table:
            self._update_table_view()
        else:
            self._update_text_view()
    
    def _update_table_view(self) -> None:
        """更新表格视图"""
        self.log_table.setRowCount(len(self.filtered_entries))
        
        for row, entry in enumerate(self.filtered_entries):
            # 时间
            time_item = QTableWidgetItem(entry.timestamp.strftime("%Y-%m-%d %H:%M:%S"))
            self.log_table.setItem(row, 0, time_item)
            
            # 级别
            level_item = QTableWidgetItem(entry.level)
            level_color = LogLevel.COLORS.get(entry.level, "#000000")
            level_item.setForeground(QColor(level_color))
            self.log_table.setItem(row, 1, level_item)
            
            # 记录器
            logger_item = QTableWidgetItem(entry.logger)
            self.log_table.setItem(row, 2, logger_item)
            
            # 消息
            message_item = QTableWidgetItem(entry.message)
            self.log_table.setItem(row, 3, message_item)
    
    def _update_text_view(self) -> None:
        """更新文本视图"""
        text_content = []
        
        for entry in self.filtered_entries:
            line = f"{entry.timestamp.strftime('%Y-%m-%d %H:%M:%S')} - {entry.logger} - {entry.level} - {entry.message}"
            text_content.append(line)
        
        self.log_text.setPlainText("\n".join(text_content))
    
    def _update_stats(self) -> None:
        """更新统计信息"""
        total_count = len(self.filtered_entries)
        level_counts = {}
        
        for entry in self.filtered_entries:
            level_counts[entry.level] = level_counts.get(entry.level, 0) + 1
        
        stats_text = f"总计: {total_count} 条日志"
        if level_counts:
            level_stats = ", ".join([f"{level}: {count}" for level, count in level_counts.items()])
            stats_text += f"\n{level_stats}"
        
        self.stats_label.setText(stats_text)
    
    def _update_logger_combo(self) -> None:
        """更新记录器下拉框"""
        loggers = set(entry.logger for entry in self.log_entries)
        
        current_selection = self.logger_combo.currentData()
        self.logger_combo.clear()
        self.logger_combo.addItem("全部记录器", "")
        
        for logger in sorted(loggers):
            self.logger_combo.addItem(logger, logger)
        
        # 恢复之前的选择
        if current_selection:
            index = self.logger_combo.findData(current_selection)
            if index >= 0:
                self.logger_combo.setCurrentIndex(index)
    
    def _refresh_logs(self) -> None:
        """刷新日志"""
        self.status_label.setText("正在刷新日志...")
        
        # 这里可以实现从文件或数据库重新加载日志的逻辑
        # 目前只是重新应用过滤器
        self._apply_filters()
        
        self.status_label.setText("日志刷新完成")
        self.logger.debug("日志已刷新")
    
    def _clear_logs(self) -> None:
        """清空日志"""
        reply = QMessageBox.question(self, "确认清空", "确定要清空所有日志吗？",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.log_entries.clear()
            self.filtered_entries.clear()
            self._update_display()
            self._update_stats()
            self._update_logger_combo()
            
            self.status_label.setText("日志已清空")
            self.logger.info("日志已清空")
    
    def _export_logs(self) -> None:
        """导出日志"""
        if not self.filtered_entries:
            QMessageBox.information(self, "提示", "没有可导出的日志")
            return
        
        # 选择导出格式
        formats = ["TXT文件 (*.txt)", "CSV文件 (*.csv)", "JSON文件 (*.json)"]
        format_dialog = QMessageBox()
        format_dialog.setWindowTitle("选择导出格式")
        format_dialog.setText("请选择导出格式:")
        
        txt_button = format_dialog.addButton("TXT", QMessageBox.ActionRole)
        csv_button = format_dialog.addButton("CSV", QMessageBox.ActionRole)
        json_button = format_dialog.addButton("JSON", QMessageBox.ActionRole)
        format_dialog.addButton(QMessageBox.Cancel)
        
        format_dialog.exec()
        clicked_button = format_dialog.clickedButton()
        
        if clicked_button == txt_button:
            self._export_to_txt()
        elif clicked_button == csv_button:
            self._export_to_csv()
        elif clicked_button == json_button:
            self._export_to_json()
    
    def _export_to_txt(self) -> None:
        """导出为TXT文件"""
        file_path, _ = QFileDialog.getSaveFileName(self, "导出TXT文件", 
                                                  f"logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                                                  "TXT文件 (*.txt)")
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    for entry in self.filtered_entries:
                        line = f"{entry.timestamp.strftime('%Y-%m-%d %H:%M:%S')} - {entry.logger} - {entry.level} - {entry.message}\n"
                        f.write(line)
                
                self.log_exported.emit("TXT", file_path)
                QMessageBox.information(self, "导出成功", f"日志已导出到: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"导出失败: {e}")
    
    def _export_to_csv(self) -> None:
        """导出为CSV文件"""
        file_path, _ = QFileDialog.getSaveFileName(self, "导出CSV文件",
                                                  f"logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                                                  "CSV文件 (*.csv)")
        
        if file_path:
            try:
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(["时间", "记录器", "级别", "消息"])
                    
                    for entry in self.filtered_entries:
                        writer.writerow([
                            entry.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                            entry.logger,
                            entry.level,
                            entry.message
                        ])
                
                self.log_exported.emit("CSV", file_path)
                QMessageBox.information(self, "导出成功", f"日志已导出到: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"导出失败: {e}")
    
    def _export_to_json(self) -> None:
        """导出为JSON文件"""
        file_path, _ = QFileDialog.getSaveFileName(self, "导出JSON文件",
                                                  f"logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                                                  "JSON文件 (*.json)")
        
        if file_path:
            try:
                data = [entry.to_dict() for entry in self.filtered_entries]
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                self.log_exported.emit("JSON", file_path)
                QMessageBox.information(self, "导出成功", f"日志已导出到: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"导出失败: {e}")
    
    def _show_settings(self) -> None:
        """显示设置对话框"""
        # 这里可以实现日志查看器的设置对话框
        QMessageBox.information(self, "设置", "日志查看器设置功能正在开发中...")
    
    def add_log_entry(self, entry: LogEntry) -> None:
        """
        添加日志条目
        
        Args:
            entry: 日志条目
        """
        self.log_entries.append(entry)
        self._apply_filters()
        self._update_logger_combo()
    
    def start_file_monitoring(self, file_path: str) -> None:
        """
        开始监控日志文件
        
        Args:
            file_path: 日志文件路径
        """
        if self.file_watcher:
            self.file_watcher.stop()
        
        self.file_watcher = LogFileWatcher(file_path)
        self.file_watcher.new_log_entry.connect(self.add_log_entry)
        self.file_watcher.start()
        
        self.status_label.setText(f"正在监控文件: {file_path}")
        self.logger.info(f"开始监控日志文件: {file_path}")
    
    def stop_file_monitoring(self) -> None:
        """停止文件监控"""
        if self.file_watcher:
            self.file_watcher.stop()
            self.file_watcher = None
            self.status_label.setText("文件监控已停止")
            self.logger.info("日志文件监控已停止")
    
    def cleanup(self) -> None:
        """清理资源"""
        if self.refresh_timer.isActive():
            self.refresh_timer.stop()
        
        self.stop_file_monitoring()
        super().cleanup()