"""
PySide6桌面软件开发框架

一个模块化、可扩展的桌面应用程序开发框架，提供现代化的UI设计和完整的CRUD生成工具。

主要特性：
- 基于PySide6的现代化UI设计
- MVC架构模式
- 主题系统（日间/夜间模式）
- 预定义界面模块（仪表盘、日志查看器、设置界面）
- CRUD代码生成工具
- 完整的测试覆盖

作者: PySide6框架开发团队
版本: 1.0.0
许可证: MIT
"""

__version__ = "1.0.0"
__author__ = "PySide6框架开发团队"
__license__ = "MIT"

# 导入核心模块
from .core.application import Application
from .core.theme_manager import ThemeManager

# 导入主要视图
from .views.main_window import MainWindow

__all__ = [
    "Application",
    "ThemeManager", 
    "MainWindow",
    "__version__",
    "__author__",
    "__license__"
]