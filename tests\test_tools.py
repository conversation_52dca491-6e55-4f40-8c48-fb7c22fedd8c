"""
工具模块测试

测试工具模块的功能，包括：
- CRUDGenerator类测试
- FieldDefinition类测试
- CodeGenerator类测试
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtTest import QTest

from tools.crud_generator import (CRUDGenerator, FieldDefinition, CodeGenerator, 
                                 PythonSyntaxHighlighter)


@pytest.fixture(scope="session")
def qapp():
    """创建QApplication实例"""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    yield app


class TestFieldDefinition:
    """FieldDefinition类测试"""
    
    def test_field_definition_creation(self):
        """测试字段定义创建"""
        field = FieldDefinition(
            name="username",
            field_type="string",
            required=True,
            default_value="",
            max_length=50,
            choices=[],
            description="用户名"
        )
        
        assert field.name == "username"
        assert field.field_type == "string"
        assert field.required is True
        assert field.max_length == 50
        assert field.description == "用户名"
    
    def test_field_definition_serialization(self):
        """测试字段定义序列化"""
        field = FieldDefinition("email", "string", True, "", 100, [], "邮箱地址")
        
        # 转换为字典
        data = field.to_dict()
        expected_keys = ["name", "field_type", "required", "default_value", 
                        "max_length", "choices", "description"]
        for key in expected_keys:
            assert key in data
        
        assert data["name"] == "email"
        assert data["field_type"] == "string"
        assert data["required"] is True
        
        # 从字典创建
        new_field = FieldDefinition.from_dict(data)
        assert new_field.name == field.name
        assert new_field.field_type == field.field_type
        assert new_field.required == field.required
        assert new_field.max_length == field.max_length
    
    def test_field_definition_with_choices(self):
        """测试带选择项的字段定义"""
        choices = ["red", "green", "blue"]
        field = FieldDefinition("color", "string", False, "red", None, choices, "颜色")
        
        assert field.choices == choices
        assert field.default_value == "red"
        
        # 序列化测试
        data = field.to_dict()
        assert data["choices"] == choices
        
        new_field = FieldDefinition.from_dict(data)
        assert new_field.choices == choices


class TestCodeGenerator:
    """CodeGenerator类测试"""
    
    def test_code_generator_creation(self):
        """测试代码生成器创建"""
        fields = [
            FieldDefinition("id", "integer", False, "0", None, [], "主键"),
            FieldDefinition("name", "string", True, "", 100, [], "名称"),
            FieldDefinition("email", "string", True, "", 255, [], "邮箱"),
            FieldDefinition("age", "integer", False, "0", None, [], "年龄"),
        ]
        
        generator = CodeGenerator("user", fields)
        
        assert generator.table_name == "user"
        assert generator.class_name == "User"
        assert len(generator.fields) == 4
    
    def test_class_name_conversion(self):
        """测试类名转换"""
        generator = CodeGenerator("user_profile", [])
        assert generator.class_name == "UserProfile"
        
        generator = CodeGenerator("product-category", [])
        assert generator.class_name == "ProductCategory"
        
        generator = CodeGenerator("order_item_detail", [])
        assert generator.class_name == "OrderItemDetail"
    
    def test_python_type_mapping(self):
        """测试Python类型映射"""
        generator = CodeGenerator("test", [])
        
        assert generator._get_python_type("string") == "str"
        assert generator._get_python_type("integer") == "int"
        assert generator._get_python_type("float") == "float"
        assert generator._get_python_type("boolean") == "bool"
        assert generator._get_python_type("date") == "datetime.date"
        assert generator._get_python_type("datetime") == "datetime.datetime"
        assert generator._get_python_type("unknown") == "str"  # 默认类型
    
    def test_model_code_generation(self):
        """测试Model代码生成"""
        fields = [
            FieldDefinition("id", "integer", False, "0", None, [], "主键"),
            FieldDefinition("name", "string", True, "", 50, [], "名称"),
            FieldDefinition("email", "string", True, "", 255, [], "邮箱"),
            FieldDefinition("is_active", "boolean", False, "True", None, [], "是否激活"),
        ]
        
        generator = CodeGenerator("user", fields)
        model_code = generator.generate_model()
        
        # 检查生成的代码包含必要的元素
        assert "class User(BaseModel):" in model_code
        assert "from ..core.models import BaseModel" in model_code
        assert "self.add_field('name', str" in model_code
        assert "self.add_field('email', str" in model_code
        assert "self.add_field('is_active', bool" in model_code
        assert "required=True" in model_code
        assert "max_length=50" in model_code
    
    def test_view_code_generation(self):
        """测试View代码生成"""
        fields = [
            FieldDefinition("id", "integer", False, "0", None, [], "主键"),
            FieldDefinition("name", "string", True, "", 50, [], "名称"),
            FieldDefinition("description", "text", False, "", None, [], "描述"),
            FieldDefinition("status", "string", False, "active", None, 
                          ["active", "inactive"], "状态"),
        ]
        
        generator = CodeGenerator("product", fields)
        view_code = generator.generate_view()
        
        # 检查生成的代码包含必要的元素
        assert "class ProductView(BaseView):" in view_code
        assert "from ..views.base_view import BaseView" in view_code
        assert "QLineEdit" in view_code  # 字符串字段
        assert "QTextEdit" in view_code  # 文本字段
        assert "QComboBox" in view_code  # 选择字段
        assert "item_created = Signal" in view_code
        assert "item_updated = Signal" in view_code
        assert "item_deleted = Signal" in view_code
    
    def test_controller_code_generation(self):
        """测试Controller代码生成"""
        fields = [
            FieldDefinition("id", "integer", False, "0", None, [], "主键"),
            FieldDefinition("title", "string", True, "", 100, [], "标题"),
            FieldDefinition("content", "text", False, "", None, [], "内容"),
        ]
        
        generator = CodeGenerator("article", fields)
        controller_code = generator.generate_controller()
        
        # 检查生成的代码包含必要的元素
        assert "class ArticleController(CRUDController):" in controller_code
        assert "from ..core.controllers import CRUDController" in controller_code
        assert "_on_item_created" in controller_code
        assert "_on_item_updated" in controller_code
        assert "_on_item_deleted" in controller_code
        assert "search_items" in controller_code
        assert "get_statistics" in controller_code
    
    def test_field_types_in_generated_code(self):
        """测试不同字段类型在生成代码中的处理"""
        fields = [
            FieldDefinition("text_field", "string", True, "", 100, [], "文本字段"),
            FieldDefinition("number_field", "integer", False, "0", None, [], "数字字段"),
            FieldDefinition("flag_field", "boolean", False, "False", None, [], "布尔字段"),
            FieldDefinition("date_field", "date", False, "", None, [], "日期字段"),
            FieldDefinition("datetime_field", "datetime", False, "", None, [], "日期时间字段"),
            FieldDefinition("long_text", "text", False, "", None, [], "长文本字段"),
            FieldDefinition("choice_field", "string", False, "option1", None, 
                          ["option1", "option2", "option3"], "选择字段"),
        ]
        
        generator = CodeGenerator("test_entity", fields)
        
        # 测试Model生成
        model_code = generator.generate_model()
        assert "str" in model_code  # 字符串类型
        assert "int" in model_code  # 整数类型
        assert "bool" in model_code  # 布尔类型
        assert "datetime.date" in model_code  # 日期类型
        assert "datetime.datetime" in model_code  # 日期时间类型
        
        # 测试View生成
        view_code = generator.generate_view()
        assert "QLineEdit" in view_code  # 文本输入框
        assert "QCheckBox" in view_code  # 复选框
        assert "QDateEdit" in view_code  # 日期选择器
        assert "QDateTimeEdit" in view_code  # 日期时间选择器
        assert "QTextEdit" in view_code  # 文本区域
        assert "QComboBox" in view_code  # 下拉框


class TestPythonSyntaxHighlighter:
    """PythonSyntaxHighlighter类测试"""
    
    def test_syntax_highlighter_creation(self, qapp):
        """测试语法高亮器创建"""
        from PySide6.QtWidgets import QTextEdit
        
        text_edit = QTextEdit()
        highlighter = PythonSyntaxHighlighter(text_edit.document())
        
        assert highlighter is not None
        assert len(highlighter.highlighting_rules) > 0
    
    def test_keyword_highlighting(self, qapp):
        """测试关键字高亮"""
        from PySide6.QtWidgets import QTextEdit
        
        text_edit = QTextEdit()
        highlighter = PythonSyntaxHighlighter(text_edit.document())
        
        # 设置包含Python关键字的文本
        test_code = """
def test_function():
    if True:
        return "Hello World"
    else:
        pass
"""
        text_edit.setPlainText(test_code)
        
        # 语法高亮应该被应用（这里主要测试不会出错）
        assert text_edit.toPlainText() == test_code


class TestCRUDGenerator:
    """CRUDGenerator类测试"""
    
    def test_crud_generator_creation(self, qapp):
        """测试CRUD生成器创建"""
        generator = CRUDGenerator()
        
        assert generator.is_initialized()
        assert len(generator.fields) == 0
        assert generator.code_generator is None
        assert len(generator.field_types) > 0
        assert len(generator.database_types) > 0
    
    def test_field_collection(self, qapp):
        """测试字段收集"""
        generator = CRUDGenerator()
        generator.show()
        
        # 模拟添加字段到表格
        generator._add_field()
        generator._add_field()
        
        # 收集字段
        fields = generator._collect_fields()
        
        # 应该包含默认字段加上新添加的字段
        assert len(fields) >= 4  # 默认有4个字段
    
    def test_table_name_validation(self, qapp):
        """测试表名验证"""
        generator = CRUDGenerator()
        
        # 设置表名
        generator.table_name_input.setText("test_table")
        assert generator.table_name_input.text() == "test_table"
        
        # 清空表名
        generator.table_name_input.clear()
        assert generator.table_name_input.text() == ""
    
    def test_field_addition_removal(self, qapp):
        """测试字段添加和删除"""
        generator = CRUDGenerator()
        generator.show()
        
        initial_row_count = generator.field_table.rowCount()
        
        # 添加字段
        generator._add_field()
        assert generator.field_table.rowCount() == initial_row_count + 1
        
        # 选择最后一行并删除
        generator.field_table.selectRow(generator.field_table.rowCount() - 1)
        generator._remove_field()
        assert generator.field_table.rowCount() == initial_row_count
    
    def test_code_generation_process(self, qapp):
        """测试代码生成过程"""
        generator = CRUDGenerator()
        generator.show()
        
        # 设置表名
        generator.table_name_input.setText("test_entity")
        
        # 模拟生成代码（不实际生成，避免复杂的UI交互）
        with patch.object(generator, '_collect_fields') as mock_collect:
            mock_fields = [
                FieldDefinition("id", "integer", False, "0", None, [], "主键"),
                FieldDefinition("name", "string", True, "", 50, [], "名称"),
            ]
            mock_collect.return_value = mock_fields
            
            # 调用生成代码方法
            generator._generate_code()
            
            # 验证代码生成器被创建
            assert generator.code_generator is not None
            assert generator.code_generator.table_name == "test_entity"
            assert len(generator.code_generator.fields) == 2
    
    def test_code_export(self, qapp):
        """测试代码导出"""
        generator = CRUDGenerator()
        
        # 创建模拟的代码生成器
        fields = [FieldDefinition("id", "integer", False, "0", None, [], "主键")]
        generator.code_generator = CodeGenerator("test", fields)
        
        # 设置预览内容
        generator.model_preview.setPlainText("# Model code")
        generator.view_preview.setPlainText("# View code")
        generator.controller_preview.setPlainText("# Controller code")
        
        # 模拟文件对话框和文件写入
        with patch('PySide6.QtWidgets.QFileDialog.getExistingDirectory') as mock_dialog:
            mock_dialog.return_value = "/tmp/export"
            
            with patch('builtins.open', create=True) as mock_open:
                mock_file = MagicMock()
                mock_open.return_value.__enter__.return_value = mock_file
                
                generator._export_code()
                
                # 验证文件写入被调用
                assert mock_file.write.call_count >= 1
    
    def test_generation_options(self, qapp):
        """测试生成选项"""
        generator = CRUDGenerator()
        
        # 测试默认选项
        assert generator.generate_model_cb.isChecked()
        assert generator.generate_view_cb.isChecked()
        assert generator.generate_controller_cb.isChecked()
        
        # 修改选项
        generator.generate_model_cb.setChecked(False)
        assert not generator.generate_model_cb.isChecked()
        
        generator.generate_view_cb.setChecked(False)
        assert not generator.generate_view_cb.isChecked()
    
    def test_database_type_selection(self, qapp):
        """测试数据库类型选择"""
        generator = CRUDGenerator()
        
        # 测试数据库类型下拉框
        assert generator.database_combo.count() > 0
        
        # 设置数据库类型
        generator.database_combo.setCurrentText("MySQL")
        assert generator.database_combo.currentText() == "MySQL"
    
    def test_field_type_options(self, qapp):
        """测试字段类型选项"""
        generator = CRUDGenerator()
        
        # 验证字段类型列表
        expected_types = ["string", "integer", "float", "boolean", 
                         "date", "datetime", "text", "email", "url", "phone"]
        
        for field_type in expected_types:
            assert field_type in generator.field_types
    
    def test_preview_tabs(self, qapp):
        """测试预览标签页"""
        generator = CRUDGenerator()
        generator.show()
        
        # 验证预览标签页
        assert generator.preview_tabs.count() == 3
        
        # 验证每个标签页都有对应的文本编辑器
        assert generator.model_preview is not None
        assert generator.view_preview is not None
        assert generator.controller_preview is not None
        
        # 验证语法高亮器
        assert generator.model_highlighter is not None
        assert generator.view_highlighter is not None
        assert generator.controller_highlighter is not None
    
    def test_cleanup(self, qapp):
        """测试清理"""
        generator = CRUDGenerator()
        generator.cleanup()
        
        # 清理后应该没有异常


class TestIntegration:
    """集成测试"""
    
    def test_complete_crud_generation_workflow(self, qapp):
        """测试完整的CRUD生成工作流程"""
        # 创建字段定义
        fields = [
            FieldDefinition("id", "integer", False, "0", None, [], "主键ID"),
            FieldDefinition("username", "string", True, "", 50, [], "用户名"),
            FieldDefinition("email", "string", True, "", 255, [], "邮箱地址"),
            FieldDefinition("age", "integer", False, "18", None, [], "年龄"),
            FieldDefinition("is_active", "boolean", False, "True", None, [], "是否激活"),
            FieldDefinition("created_at", "datetime", False, "", None, [], "创建时间"),
        ]
        
        # 创建代码生成器
        generator = CodeGenerator("user", fields)
        
        # 生成所有代码
        model_code = generator.generate_model()
        view_code = generator.generate_view()
        controller_code = generator.generate_controller()
        
        # 验证生成的代码包含必要的元素
        
        # Model验证
        assert "class User(BaseModel):" in model_code
        assert "username" in model_code
        assert "email" in model_code
        assert "required=True" in model_code
        
        # View验证
        assert "class UserView(BaseView):" in view_code
        assert "QLineEdit" in view_code
        assert "QCheckBox" in view_code
        assert "QDateTimeEdit" in view_code
        
        # Controller验证
        assert "class UserController(CRUDController):" in controller_code
        assert "_on_item_created" in controller_code
        assert "search_items" in controller_code
        
        # 验证代码语法正确性（简单检查）
        assert model_code.count("def ") >= 3  # 至少有几个方法
        assert view_code.count("def ") >= 5   # 视图应该有更多方法
        assert controller_code.count("def ") >= 5  # 控制器也应该有多个方法


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])