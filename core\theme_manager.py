"""
主题管理器

负责管理应用程序的主题系统，包括：
- 主题切换（日间/夜间模式）
- 样式表加载和应用
- 主题相关的资源管理
- 主题变更通知
"""

import os
import logging
from typing import Dict, Optional
from pathlib import Path

from PySide6.QtCore import QObject, Signal
from PySide6.QtWidgets import QApplication
from PySide6.QtGui import QPalette, QColor


class ThemeManager(QObject):
    """
    主题管理器类
    
    管理应用程序的主题系统，支持日间和夜间模式切换。
    """
    
    # 信号定义
    theme_changed = Signal(str)  # 主题变更信号
    
    # 预定义主题
    THEMES = {
        "light": {
            "name": "日间模式",
            "description": "明亮的日间主题",
            "style_file": "light_theme.qss",
            "colors": {
                "background": "#F5F5F7",
                "surface": "#FFFFFF", 
                "primary": "#007AFF",
                "text": "#1D1D1F",
                "text_secondary": "#8E8E93",
                "border": "#D1D1D6",
                "shadow": "rgba(0, 0, 0, 0.1)"
            }
        },
        "dark": {
            "name": "夜间模式", 
            "description": "深色的夜间主题",
            "style_file": "dark_theme.qss",
            "colors": {
                "background": "#1E1E1E",
                "surface": "#2D2D2D",
                "primary": "#0A84FF", 
                "text": "#F2F2F7",
                "text_secondary": "#8E8E93",
                "border": "#38383A",
                "shadow": "rgba(0, 0, 0, 0.3)"
            }
        }
    }
    
    def __init__(self, app: QApplication):
        """
        初始化主题管理器
        
        Args:
            app: QApplication实例
        """
        super().__init__()
        
        self.app = app
        self.logger = logging.getLogger(__name__)
        
        # 当前主题
        self._current_theme = "light"
        
        # 样式表缓存
        self._style_cache: Dict[str, str] = {}
        
        # 资源路径
        self.resources_path = Path(__file__).parent.parent / "resources"
        self.styles_path = self.resources_path / "styles"
        
        self.logger.info("主题管理器初始化完成")
    
    def get_available_themes(self) -> Dict[str, Dict]:
        """
        获取可用的主题列表
        
        Returns:
            主题字典，键为主题ID，值为主题信息
        """
        return self.THEMES.copy()
    
    def get_current_theme(self) -> str:
        """
        获取当前主题ID
        
        Returns:
            当前主题ID
        """
        return self._current_theme
    
    def get_theme_info(self, theme_id: str) -> Optional[Dict]:
        """
        获取主题信息
        
        Args:
            theme_id: 主题ID
            
        Returns:
            主题信息字典或None
        """
        return self.THEMES.get(theme_id)
    
    def get_theme_color(self, color_name: str, theme_id: str = None) -> str:
        """
        获取主题颜色
        
        Args:
            color_name: 颜色名称
            theme_id: 主题ID，默认使用当前主题
            
        Returns:
            颜色值字符串
        """
        if theme_id is None:
            theme_id = self._current_theme
            
        theme_info = self.THEMES.get(theme_id)
        if theme_info and "colors" in theme_info:
            return theme_info["colors"].get(color_name, "#000000")
        
        return "#000000"
    
    def load_style_sheet(self, theme_id: str) -> str:
        """
        加载主题样式表
        
        Args:
            theme_id: 主题ID
            
        Returns:
            样式表内容
        """
        # 检查缓存
        if theme_id in self._style_cache:
            return self._style_cache[theme_id]
        
        theme_info = self.THEMES.get(theme_id)
        if not theme_info:
            self.logger.error(f"未找到主题: {theme_id}")
            return ""
        
        style_file = theme_info["style_file"]
        style_path = self.styles_path / style_file
        
        try:
            if style_path.exists():
                with open(style_path, 'r', encoding='utf-8') as f:
                    style_content = f.read()
                
                # 替换颜色变量
                colors = theme_info.get("colors", {})
                for color_name, color_value in colors.items():
                    placeholder = f"${{{color_name}}}"
                    style_content = style_content.replace(placeholder, color_value)
                
                # 缓存样式表
                self._style_cache[theme_id] = style_content
                
                self.logger.info(f"成功加载主题样式表: {style_file}")
                return style_content
            else:
                self.logger.warning(f"样式表文件不存在: {style_path}")
                return self._generate_default_style(theme_id)
                
        except Exception as e:
            self.logger.error(f"加载样式表失败: {e}")
            return self._generate_default_style(theme_id)
    
    def _generate_default_style(self, theme_id: str) -> str:
        """
        生成默认样式表
        
        Args:
            theme_id: 主题ID
            
        Returns:
            默认样式表内容
        """
        theme_info = self.THEMES.get(theme_id, self.THEMES["light"])
        colors = theme_info.get("colors", {})
        
        default_style = f"""
        /* 默认样式表 - {theme_info.get('name', '未知主题')} */
        
        QWidget {{
            background-color: {colors.get('background', '#F5F5F7')};
            color: {colors.get('text', '#1D1D1F')};
            font-family: 'Microsoft YaHei UI', 'Segoe UI', sans-serif;
        }}
        
        QPushButton {{
            background-color: {colors.get('primary', '#007AFF')};
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
        }}
        
        QPushButton:hover {{
            background-color: {colors.get('primary', '#007AFF')};
            opacity: 0.8;
        }}
        
        QPushButton:pressed {{
            background-color: {colors.get('primary', '#007AFF')};
            opacity: 0.6;
        }}
        
        QLineEdit {{
            background-color: {colors.get('surface', '#FFFFFF')};
            border: 1px solid {colors.get('border', '#D1D1D6')};
            border-radius: 6px;
            padding: 8px 12px;
        }}
        
        QLineEdit:focus {{
            border-color: {colors.get('primary', '#007AFF')};
        }}
        """
        
        return default_style
    
    def set_theme(self, theme_id: str) -> bool:
        """
        设置主题
        
        Args:
            theme_id: 主题ID
            
        Returns:
            是否设置成功
        """
        if theme_id not in self.THEMES:
            self.logger.error(f"未知的主题ID: {theme_id}")
            return False
        
        if theme_id == self._current_theme:
            self.logger.info(f"主题已经是: {theme_id}")
            return True
        
        try:
            # 加载样式表
            style_sheet = self.load_style_sheet(theme_id)
            
            # 应用样式表
            self.app.setStyleSheet(style_sheet)
            
            # 更新当前主题
            old_theme = self._current_theme
            self._current_theme = theme_id
            
            # 发送主题变更信号
            self.theme_changed.emit(theme_id)
            
            theme_name = self.THEMES[theme_id]["name"]
            self.logger.info(f"主题切换成功: {old_theme} -> {theme_id} ({theme_name})")
            
            return True
            
        except Exception as e:
            self.logger.error(f"设置主题失败: {e}")
            return False
    
    def toggle_theme(self) -> str:
        """
        切换主题（在日间和夜间模式之间）
        
        Returns:
            新的主题ID
        """
        new_theme = "dark" if self._current_theme == "light" else "light"
        self.set_theme(new_theme)
        return new_theme
    
    def apply_palette(self, theme_id: str = None) -> None:
        """
        应用调色板
        
        Args:
            theme_id: 主题ID，默认使用当前主题
        """
        if theme_id is None:
            theme_id = self._current_theme
        
        theme_info = self.THEMES.get(theme_id)
        if not theme_info:
            return
        
        colors = theme_info.get("colors", {})
        
        palette = QPalette()
        
        # 设置基础颜色
        palette.setColor(QPalette.Window, QColor(colors.get("background", "#F5F5F7")))
        palette.setColor(QPalette.WindowText, QColor(colors.get("text", "#1D1D1F")))
        palette.setColor(QPalette.Base, QColor(colors.get("surface", "#FFFFFF")))
        palette.setColor(QPalette.AlternateBase, QColor(colors.get("border", "#D1D1D6")))
        palette.setColor(QPalette.Text, QColor(colors.get("text", "#1D1D1F")))
        palette.setColor(QPalette.Button, QColor(colors.get("surface", "#FFFFFF")))
        palette.setColor(QPalette.ButtonText, QColor(colors.get("text", "#1D1D1F")))
        palette.setColor(QPalette.Highlight, QColor(colors.get("primary", "#007AFF")))
        palette.setColor(QPalette.HighlightedText, QColor("#FFFFFF"))
        
        self.app.setPalette(palette)
        self.logger.info(f"调色板应用完成: {theme_id}")
    
    def clear_cache(self) -> None:
        """清除样式表缓存"""
        self._style_cache.clear()
        self.logger.info("样式表缓存已清除")
    
    def reload_current_theme(self) -> None:
        """重新加载当前主题"""
        # 清除当前主题的缓存
        if self._current_theme in self._style_cache:
            del self._style_cache[self._current_theme]
        
        # 重新设置主题
        self.set_theme(self._current_theme)
        self.logger.info(f"当前主题已重新加载: {self._current_theme}")