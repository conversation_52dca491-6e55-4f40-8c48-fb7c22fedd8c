#!/usr/bin/env python3
"""
PySide6桌面软件开发框架 - 主入口文件

这是框架的主入口点，负责启动应用程序并初始化所有组件。

使用方法:
    python main.py

作者: PySide6框架开发团队
版本: 1.0.0
许可证: MIT
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPixmap, QFont

from core.application import create_application
from views.main_window import MainWindow


def setup_logging() -> None:
    """设置日志系统"""
    # 创建日志目录
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "main.log", encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("日志系统初始化完成")


def create_splash_screen() -> QSplashScreen:
    """
    创建启动画面
    
    Returns:
        启动画面对象
    """
    # 创建简单的启动画面
    pixmap = QPixmap(400, 300)
    pixmap.fill(Qt.white)
    
    splash = QSplashScreen(pixmap)
    splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
    
    # 设置启动画面文本
    splash.showMessage(
        "PySide6开发框架\n正在启动...",
        Qt.AlignCenter | Qt.AlignBottom,
        Qt.black
    )
    
    return splash


def check_dependencies() -> bool:
    """
    检查依赖项
    
    Returns:
        是否所有依赖项都可用
    """
    required_modules = [
        'PySide6',
        'PySide6.QtWidgets',
        'PySide6.QtCore',
        'PySide6.QtGui',
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        error_msg = f"缺少必需的模块: {', '.join(missing_modules)}\n\n"
        error_msg += "请运行以下命令安装依赖:\n"
        error_msg += "pip install -r requirements.txt"
        
        print(error_msg)
        return False
    
    return True


def setup_application_style(app: QApplication) -> None:
    """
    设置应用程序样式
    
    Args:
        app: 应用程序实例
    """
    # 设置应用程序图标
    # app.setWindowIcon(QIcon("resources/icons/app_icon.png"))
    
    # 设置默认字体
    font = QFont("Microsoft YaHei UI", 12)
    app.setFont(font)
    
    # 设置应用程序属性
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)


def main() -> int:
    """
    主函数
    
    Returns:
        应用程序退出代码
    """
    try:
        # 设置日志
        setup_logging()
        logger = logging.getLogger(__name__)
        
        logger.info("=" * 50)
        logger.info("PySide6桌面软件开发框架启动")
        logger.info("=" * 50)
        
        # 检查依赖项
        if not check_dependencies():
            return 1
        
        # 创建应用程序
        app = create_application()
        
        # 设置应用程序样式
        setup_application_style(app)
        
        # 创建并显示启动画面
        splash = create_splash_screen()
        splash.show()
        app.processEvents()
        
        # 模拟启动过程
        splash.showMessage("正在初始化核心组件...", Qt.AlignCenter | Qt.AlignBottom, Qt.black)
        app.processEvents()
        QTimer.singleShot(500, lambda: None)  # 等待500ms
        
        splash.showMessage("正在加载用户界面...", Qt.AlignCenter | Qt.AlignBottom, Qt.black)
        app.processEvents()
        
        # 创建主窗口
        main_window = MainWindow()
        app.set_main_window(main_window)
        
        # 连接主题切换信号
        main_window.theme_toggle_requested.connect(
            lambda: app.get_theme_manager().toggle_theme()
        )
        
        # 应用主题到主窗口
        theme_manager = app.get_theme_manager()
        main_window.apply_theme(theme_manager.get_current_theme())
        
        splash.showMessage("启动完成！", Qt.AlignCenter | Qt.AlignBottom, Qt.black)
        app.processEvents()
        QTimer.singleShot(500, lambda: None)  # 等待500ms
        
        # 显示主窗口并隐藏启动画面
        main_window.show()
        splash.finish(main_window)
        
        logger.info("应用程序启动完成")
        logger.info(f"主窗口大小: {main_window.size().width()}x{main_window.size().height()}")
        logger.info(f"当前主题: {theme_manager.get_current_theme()}")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        error_msg = f"应用程序启动失败: {e}"
        print(error_msg)
        
        # 尝试显示错误对话框
        try:
            if 'app' in locals():
                QMessageBox.critical(None, "启动错误", error_msg)
            else:
                # 如果应用程序还没创建，创建一个临时的
                temp_app = QApplication(sys.argv)
                QMessageBox.critical(None, "启动错误", error_msg)
        except:
            pass  # 如果连错误对话框都无法显示，就忽略
        
        return 1


def show_help() -> None:
    """显示帮助信息"""
    help_text = """
PySide6桌面软件开发框架 v1.0.0

这是一个现代化、模块化的桌面应用程序开发框架，基于PySide6构建。

主要特性:
- 现代化的macOS风格UI设计
- MVC架构模式
- 主题系统（日间/夜间模式）
- 预定义界面模块（仪表盘、日志查看器、设置界面）
- CRUD代码生成工具
- 完整的测试覆盖

使用方法:
    python main.py              # 启动应用程序
    python main.py --help       # 显示帮助信息
    python main.py --version    # 显示版本信息

系统要求:
- Python 3.8+
- PySide6 6.5.0+
- Windows 10+ / macOS 10.15+ / Ubuntu 20.04+

更多信息请参阅 README.md 文件。

作者: PySide6框架开发团队
许可证: MIT
项目主页: https://github.com/pyside6-framework/framework
"""
    print(help_text)


def show_version() -> None:
    """显示版本信息"""
    version_text = """
PySide6桌面软件开发框架 v1.0.0

构建信息:
- Python版本: {python_version}
- PySide6版本: {pyside_version}
- 构建日期: 2023-12-01
- 构建平台: {platform}

版权所有 © 2023 PySide6框架开发团队
""".format(
        python_version=sys.version.split()[0],
        pyside_version="6.5.0+",  # 可以动态获取
        platform=sys.platform
    )
    print(version_text)


if __name__ == "__main__":
    # 处理命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ['--help', '-h', 'help']:
            show_help()
            sys.exit(0)
        elif arg in ['--version', '-v', 'version']:
            show_version()
            sys.exit(0)
        else:
            print(f"未知参数: {sys.argv[1]}")
            print("使用 --help 查看帮助信息")
            sys.exit(1)
    
    # 启动应用程序
    exit_code = main()
    
    # 记录退出信息
    if exit_code == 0:
        print("应用程序正常退出")
    else:
        print(f"应用程序异常退出，退出代码: {exit_code}")
    
    sys.exit(exit_code)