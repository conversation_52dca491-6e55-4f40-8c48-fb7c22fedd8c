"""
CRUD生成工具

实现完整的CRUD代码生成功能，包括：
- 表结构定义界面
- 字段类型选择
- 代码生成（Model、View、Controller）
- 代码预览和语法高亮
- 代码导出功能
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QPushButton, QLineEdit, QComboBox,
                               QTableWidget, QTableWidgetItem, QTextEdit,
                               QCheckBox, QGroupBox, QSplitter, QTabWidget,
                               QHeaderView, QFileDialog, QMessageBox,
                               QScrollArea, QFrame)
from PySide6.QtCore import Qt, Signal, QThread, pyqtSignal
from PySide6.QtGui import QFont, QSyntaxHighlighter, QTextCharFormat, QColor

from ..views.base_view import BaseView


class FieldDefinition:
    """字段定义"""
    
    def __init__(self, name: str, field_type: str, required: bool = False,
                 default_value: str = "", max_length: int = None,
                 choices: List[str] = None, description: str = ""):
        """
        初始化字段定义
        
        Args:
            name: 字段名
            field_type: 字段类型
            required: 是否必填
            default_value: 默认值
            max_length: 最大长度
            choices: 选择项
            description: 描述
        """
        self.name = name
        self.field_type = field_type
        self.required = required
        self.default_value = default_value
        self.max_length = max_length
        self.choices = choices or []
        self.description = description
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "field_type": self.field_type,
            "required": self.required,
            "default_value": self.default_value,
            "max_length": self.max_length,
            "choices": self.choices,
            "description": self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FieldDefinition':
        """从字典创建"""
        return cls(
            name=data["name"],
            field_type=data["field_type"],
            required=data.get("required", False),
            default_value=data.get("default_value", ""),
            max_length=data.get("max_length"),
            choices=data.get("choices", []),
            description=data.get("description", "")
        )


class PythonSyntaxHighlighter(QSyntaxHighlighter):
    """
    Python语法高亮器
    
    为生成的Python代码提供语法高亮。
    """
    
    def __init__(self, parent=None):
        """初始化语法高亮器"""
        super().__init__(parent)
        
        # 定义高亮规则
        self.highlighting_rules = []
        
        # Python关键字
        keyword_format = QTextCharFormat()
        keyword_format.setForeground(QColor("#0000FF"))
        keyword_format.setFontWeight(QFont.Bold)
        
        keywords = [
            "and", "as", "assert", "break", "class", "continue", "def",
            "del", "elif", "else", "except", "exec", "finally", "for",
            "from", "global", "if", "import", "in", "is", "lambda",
            "not", "or", "pass", "print", "raise", "return", "try",
            "while", "with", "yield", "None", "True", "False"
        ]
        
        for keyword in keywords:
            pattern = f"\\b{keyword}\\b"
            self.highlighting_rules.append((re.compile(pattern), keyword_format))
        
        # 字符串
        string_format = QTextCharFormat()
        string_format.setForeground(QColor("#008000"))
        self.highlighting_rules.append((re.compile(r'".*?"'), string_format))
        self.highlighting_rules.append((re.compile(r"'.*?'"), string_format))
        
        # 注释
        comment_format = QTextCharFormat()
        comment_format.setForeground(QColor("#808080"))
        comment_format.setFontItalic(True)
        self.highlighting_rules.append((re.compile(r"#.*"), comment_format))
        
        # 类名
        class_format = QTextCharFormat()
        class_format.setForeground(QColor("#800080"))
        class_format.setFontWeight(QFont.Bold)
        self.highlighting_rules.append((re.compile(r"\bclass\s+(\w+)"), class_format))
        
        # 函数名
        function_format = QTextCharFormat()
        function_format.setForeground(QColor("#0000FF"))
        self.highlighting_rules.append((re.compile(r"\bdef\s+(\w+)"), function_format))
    
    def highlightBlock(self, text: str) -> None:
        """高亮文本块"""
        for pattern, format in self.highlighting_rules:
            for match in pattern.finditer(text):
                start, end = match.span()
                self.setFormat(start, end - start, format)


class CodeGenerator:
    """
    代码生成器
    
    根据字段定义生成完整的CRUD代码。
    """
    
    def __init__(self, table_name: str, fields: List[FieldDefinition]):
        """
        初始化代码生成器
        
        Args:
            table_name: 表名
            fields: 字段定义列表
        """
        self.table_name = table_name
        self.fields = fields
        self.class_name = self._to_class_name(table_name)
    
    def _to_class_name(self, table_name: str) -> str:
        """
        将表名转换为类名
        
        Args:
            table_name: 表名
            
        Returns:
            类名
        """
        # 移除特殊字符，转换为驼峰命名
        words = re.findall(r'\w+', table_name)
        return ''.join(word.capitalize() for word in words)
    
    def _get_python_type(self, field_type: str) -> str:
        """
        获取Python类型
        
        Args:
            field_type: 字段类型
            
        Returns:
            Python类型
        """
        type_mapping = {
            "string": "str",
            "integer": "int",
            "float": "float",
            "boolean": "bool",
            "date": "datetime.date",
            "datetime": "datetime.datetime",
            "text": "str",
            "email": "str",
            "url": "str",
            "phone": "str"
        }
        return type_mapping.get(field_type, "str")
    
    def generate_model(self) -> str:
        """
        生成Model代码
        
        Returns:
            Model代码
        """
        imports = [
            "from datetime import datetime, date",
            "from typing import Optional, List",
            "from ..core.models import BaseModel, FieldValidator"
        ]
        
        class_code = f"""
class {self.class_name}(BaseModel):
    \"\"\"
    {self.class_name} 数据模型
    
    自动生成的模型类，包含字段定义和验证规则。
    \"\"\"
    
    def _define_fields(self) -> None:
        \"\"\"定义模型字段\"\"\"
        super()._define_fields()
        
"""
        
        # 添加字段定义
        for field in self.fields:
            python_type = self._get_python_type(field.field_type)
            field_args = []
            
            if field.required:
                field_args.append("required=True")
            
            if field.default_value:
                if field.field_type in ["string", "text", "email", "url", "phone"]:
                    field_args.append(f'default="{field.default_value}"')
                else:
                    field_args.append(f'default={field.default_value}')
            
            if field.max_length:
                field_args.append(f"max_length={field.max_length}")
            
            if field.choices:
                choices_str = "[" + ", ".join([f'"{choice}"' for choice in field.choices]) + "]"
                field_args.append(f"choices={choices_str}")
            
            args_str = ", ".join(field_args)
            if args_str:
                args_str = ", " + args_str
            
            class_code += f'        self.add_field("{field.name}", {python_type}{args_str})\n'
        
        # 添加便捷方法
        class_code += f"""
    def __str__(self) -> str:
        \"\"\"字符串表示\"\"\"
        return f"{self.class_name}(id={{self.get_value('id')}})"
    
    @classmethod
    def create(cls, **kwargs) -> '{self.class_name}':
        \"\"\"
        创建新实例
        
        Args:
            **kwargs: 字段值
            
        Returns:
            新实例
        \"\"\"
        return cls(**kwargs)
    
    def update(self, **kwargs) -> bool:
        \"\"\"
        更新字段值
        
        Args:
            **kwargs: 要更新的字段值
            
        Returns:
            是否更新成功
        \"\"\"
        success = True
        for field_name, value in kwargs.items():
            if not self.set_value(field_name, value):
                success = False
        return success
"""
        
        return "\n".join(imports) + "\n" + class_code
    
    def generate_view(self) -> str:
        """
        生成View代码
        
        Returns:
            View代码
        """
        imports = [
            "from typing import Optional, Dict, Any",
            "from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,",
            "                               QLabel, QPushButton, QLineEdit, QComboBox,",
            "                               QCheckBox, QDateEdit, QDateTimeEdit, QTextEdit,",
            "                               QTableWidget, QTableWidgetItem, QHeaderView,",
            "                               QMessageBox, QGroupBox, QSplitter)",
            "from PySide6.QtCore import Qt, Signal",
            "from PySide6.QtGui import QFont",
            "",
            "from ..views.base_view import BaseView",
            f"from .{self.table_name}_model import {self.class_name}"
        ]
        
        class_code = f"""
class {self.class_name}View(BaseView):
    \"\"\"
    {self.class_name} 视图
    
    自动生成的视图类，包含表单和列表界面。
    \"\"\"
    
    # 信号定义
    item_created = Signal(object)  # 项目创建信号
    item_updated = Signal(object)  # 项目更新信号
    item_deleted = Signal(object)  # 项目删除信号
    item_selected = Signal(object)  # 项目选择信号
    
    def __init__(self, parent: Optional[QWidget] = None):
        \"\"\"
        初始化视图
        
        Args:
            parent: 父组件
        \"\"\"
        super().__init__(parent)
        
        # 当前编辑的项目
        self.current_item: Optional[{self.class_name}] = None
        
        # 表单字段
        self.form_fields: Dict[str, QWidget] = {{}}
    
    def setup_ui(self) -> None:
        \"\"\"设置用户界面\"\"\"
        content_widget = self.get_content_widget()
        layout = QHBoxLayout(content_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：表单区域
        self._create_form_area(splitter)
        
        # 右侧：列表区域
        self._create_list_area(splitter)
        
        # 设置分割器比例
        splitter.setSizes([400, 600])
        layout.addWidget(splitter)
    
    def _create_form_area(self, splitter: QSplitter) -> None:
        \"\"\"
        创建表单区域
        
        Args:
            splitter: 分割器
        \"\"\"
        form_widget = QWidget()
        form_layout = QVBoxLayout(form_widget)
        form_layout.setContentsMargins(0, 0, 0, 0)
        form_layout.setSpacing(16)
        
        # 表单标题
        form_group = QGroupBox("编辑 {self.class_name}")
        form_group_layout = QFormLayout(form_group)
        form_group_layout.setSpacing(12)
        
"""
        
        # 添加表单字段
        for field in self.fields:
            if field.name in ["id", "created_at", "updated_at"]:
                continue  # 跳过系统字段
            
            widget_code = self._generate_form_field_code(field)
            class_code += widget_code
        
        class_code += f"""
        form_layout.addWidget(form_group)
        
        # 按钮区域
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, 0)
        
        self.save_button = QPushButton("💾 保存")
        self.save_button.clicked.connect(self._save_item)
        button_layout.addWidget(self.save_button)
        
        self.new_button = QPushButton("➕ 新建")
        self.new_button.clicked.connect(self._new_item)
        button_layout.addWidget(self.new_button)
        
        self.delete_button = QPushButton("🗑️ 删除")
        self.delete_button.clicked.connect(self._delete_item)
        self.delete_button.setEnabled(False)
        button_layout.addWidget(self.delete_button)
        
        button_layout.addStretch()
        form_layout.addWidget(button_widget)
        
        form_layout.addStretch()
        splitter.addWidget(form_widget)
    
    def _create_list_area(self, splitter: QSplitter) -> None:
        \"\"\"
        创建列表区域
        
        Args:
            splitter: 分割器
        \"\"\"
        list_widget = QWidget()
        list_layout = QVBoxLayout(list_widget)
        list_layout.setContentsMargins(0, 0, 0, 0)
        list_layout.setSpacing(16)
        
        # 列表标题
        list_group = QGroupBox("{self.class_name} 列表")
        list_group_layout = QVBoxLayout(list_group)
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        
        # 设置列
        columns = [field.name for field in self.fields]
        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)
        
        # 设置列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        if len(columns) > 0:
            header.setSectionResizeMode(len(columns) - 1, QHeaderView.Stretch)
        
        list_group_layout.addWidget(self.table)
        list_layout.addWidget(list_group)
        
        splitter.addWidget(list_widget)
"""
        
        # 添加方法
        class_code += self._generate_view_methods()
        
        return "\n".join(imports) + "\n" + class_code
    
    def _generate_form_field_code(self, field: FieldDefinition) -> str:
        """
        生成表单字段代码
        
        Args:
            field: 字段定义
            
        Returns:
            字段代码
        """
        field_name = field.name
        field_type = field.field_type
        
        if field_type == "boolean":
            return f"""        # {field.description or field_name}
        self.form_fields["{field_name}"] = QCheckBox()
        form_group_layout.addRow("{field_name}:", self.form_fields["{field_name}"])
        
"""
        elif field_type in ["date"]:
            return f"""        # {field.description or field_name}
        self.form_fields["{field_name}"] = QDateEdit()
        self.form_fields["{field_name}"].setCalendarPopup(True)
        form_group_layout.addRow("{field_name}:", self.form_fields["{field_name}"])
        
"""
        elif field_type in ["datetime"]:
            return f"""        # {field.description or field_name}
        self.form_fields["{field_name}"] = QDateTimeEdit()
        self.form_fields["{field_name}"].setCalendarPopup(True)
        form_group_layout.addRow("{field_name}:", self.form_fields["{field_name}"])
        
"""
        elif field_type == "text":
            return f"""        # {field.description or field_name}
        self.form_fields["{field_name}"] = QTextEdit()
        self.form_fields["{field_name}"].setMaximumHeight(100)
        form_group_layout.addRow("{field_name}:", self.form_fields["{field_name}"])
        
"""
        elif field.choices:
            return f"""        # {field.description or field_name}
        self.form_fields["{field_name}"] = QComboBox()
        self.form_fields["{field_name}"].addItems({field.choices})
        form_group_layout.addRow("{field_name}:", self.form_fields["{field_name}"])
        
"""
        else:
            return f"""        # {field.description or field_name}
        self.form_fields["{field_name}"] = QLineEdit()
        form_group_layout.addRow("{field_name}:", self.form_fields["{field_name}"])
        
"""
    
    def _generate_view_methods(self) -> str:
        """
        生成视图方法
        
        Returns:
            方法代码
        """
        return f"""
    def _save_item(self) -> None:
        \"\"\"保存项目\"\"\"
        try:
            # 收集表单数据
            data = {{}}
            for field_name, widget in self.form_fields.items():
                if isinstance(widget, QLineEdit):
                    data[field_name] = widget.text()
                elif isinstance(widget, QTextEdit):
                    data[field_name] = widget.toPlainText()
                elif isinstance(widget, QComboBox):
                    data[field_name] = widget.currentText()
                elif isinstance(widget, QCheckBox):
                    data[field_name] = widget.isChecked()
                elif isinstance(widget, (QDateEdit, QDateTimeEdit)):
                    data[field_name] = widget.dateTime().toPython()
            
            if self.current_item:
                # 更新现有项目
                success = self.current_item.update(**data)
                if success:
                    self.item_updated.emit(self.current_item)
                    QMessageBox.information(self, "成功", "项目已更新")
                else:
                    QMessageBox.warning(self, "警告", "更新失败，请检查输入数据")
            else:
                # 创建新项目
                new_item = {self.class_name}.create(**data)
                is_valid, errors = new_item.validate()
                if is_valid:
                    self.item_created.emit(new_item)
                    QMessageBox.information(self, "成功", "项目已创建")
                    self._clear_form()
                else:
                    error_msg = "\\n".join([f"{{k}}: {{v}}" for k, v in errors.items()])
                    QMessageBox.warning(self, "验证失败", error_msg)
                    
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败: {{e}}")
    
    def _new_item(self) -> None:
        \"\"\"新建项目\"\"\"
        self.current_item = None
        self._clear_form()
        self.delete_button.setEnabled(False)
    
    def _delete_item(self) -> None:
        \"\"\"删除项目\"\"\"
        if not self.current_item:
            return
        
        reply = QMessageBox.question(self, "确认删除", 
                                   "确定要删除这个项目吗？",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.item_deleted.emit(self.current_item)
            self._new_item()
    
    def _on_selection_changed(self) -> None:
        \"\"\"选择变更处理\"\"\"
        current_row = self.table.currentRow()
        if current_row >= 0:
            # 这里需要从控制器获取对应的项目对象
            # 暂时创建一个空对象作为示例
            self.current_item = {self.class_name}()
            self._load_form_data()
            self.delete_button.setEnabled(True)
            self.item_selected.emit(self.current_item)
    
    def _clear_form(self) -> None:
        \"\"\"清空表单\"\"\"
        for widget in self.form_fields.values():
            if isinstance(widget, QLineEdit):
                widget.clear()
            elif isinstance(widget, QTextEdit):
                widget.clear()
            elif isinstance(widget, QComboBox):
                widget.setCurrentIndex(0)
            elif isinstance(widget, QCheckBox):
                widget.setChecked(False)
            elif isinstance(widget, (QDateEdit, QDateTimeEdit)):
                widget.setDateTime(widget.minimumDateTime())
    
    def _load_form_data(self) -> None:
        \"\"\"加载表单数据\"\"\"
        if not self.current_item:
            return
        
        for field_name, widget in self.form_fields.items():
            value = self.current_item.get_value(field_name)
            if value is None:
                continue
                
            if isinstance(widget, QLineEdit):
                widget.setText(str(value))
            elif isinstance(widget, QTextEdit):
                widget.setPlainText(str(value))
            elif isinstance(widget, QComboBox):
                index = widget.findText(str(value))
                if index >= 0:
                    widget.setCurrentIndex(index)
            elif isinstance(widget, QCheckBox):
                widget.setChecked(bool(value))
            elif isinstance(widget, (QDateEdit, QDateTimeEdit)):
                if hasattr(value, 'toPython'):
                    widget.setDateTime(value)
    
    def update_table(self, items: List[{self.class_name}]) -> None:
        \"\"\"
        更新表格数据
        
        Args:
            items: 项目列表
        \"\"\"
        self.table.setRowCount(len(items))
        
        for row, item in enumerate(items):
            for col, field in enumerate(self.fields):
                value = item.get_value(field.name)
                if value is not None:
                    self.table.setItem(row, col, QTableWidgetItem(str(value)))
                else:
                    self.table.setItem(row, col, QTableWidgetItem(""))
    
    def get_current_item(self) -> Optional[{self.class_name}]:
        \"\"\"
        获取当前选择的项目
        
        Returns:
            当前项目或None
        \"\"\"
        return self.current_item
"""
    
    def generate_controller(self) -> str:
        """
        生成Controller代码
        
        Returns:
            Controller代码
        """
        imports = [
            "from typing import List, Optional, Dict, Any",
            "from PySide6.QtCore import QObject",
            "",
            "from ..core.controllers import CRUDController",
            f"from .{self.table_name}_model import {self.class_name}",
            f"from .{self.table_name}_view import {self.class_name}View"
        ]
        
        class_code = f"""
class {self.class_name}Controller(CRUDController):
    \"\"\"
    {self.class_name} 控制器
    
    自动生成的控制器类，处理 {self.class_name} 的业务逻辑。
    \"\"\"
    
    def __init__(self):
        \"\"\"初始化控制器\"\"\"
        # 创建模型和视图
        model = {self.class_name}()
        view = {self.class_name}View()
        
        super().__init__(model, view)
        
        # 连接视图信号
        self._connect_view_signals()
    
    def _connect_view_signals(self) -> None:
        \"\"\"连接视图信号\"\"\"
        view = self.get_view()
        if view:
            view.item_created.connect(self._on_item_created)
            view.item_updated.connect(self._on_item_updated)
            view.item_deleted.connect(self._on_item_deleted)
            view.item_selected.connect(self._on_item_selected)
    
    def _on_item_created(self, item: {self.class_name}) -> None:
        \"\"\"
        处理项目创建
        
        Args:
            item: 创建的项目
        \"\"\"
        # 添加到数据存储
        self._items.append(item)
        
        # 更新视图
        self._update_view()
        
        self.logger.info(f"项目已创建: {{item}}")
    
    def _on_item_updated(self, item: {self.class_name}) -> None:
        \"\"\"
        处理项目更新
        
        Args:
            item: 更新的项目
        \"\"\"
        # 更新视图
        self._update_view()
        
        self.logger.info(f"项目已更新: {{item}}")
    
    def _on_item_deleted(self, item: {self.class_name}) -> None:
        \"\"\"
        处理项目删除
        
        Args:
            item: 删除的项目
        \"\"\"
        # 从数据存储中移除
        if item in self._items:
            self._items.remove(item)
        
        # 更新视图
        self._update_view()
        
        self.logger.info(f"项目已删除: {{item}}")
    
    def _on_item_selected(self, item: {self.class_name}) -> None:
        \"\"\"
        处理项目选择
        
        Args:
            item: 选择的项目
        \"\"\"
        self.logger.debug(f"项目已选择: {{item}}")
    
    def _update_view(self) -> None:
        \"\"\"更新视图\"\"\"
        view = self.get_view()
        if view and hasattr(view, 'update_table'):
            view.update_table(self._items)
    
    def initialize(self) -> bool:
        \"\"\"
        初始化控制器
        
        Returns:
            是否初始化成功
        \"\"\"
        # 加载初始数据
        self._load_initial_data()
        
        # 更新视图
        self._update_view()
        
        self.set_status("ready")
        self.logger.info(f"{self.class_name}Controller 初始化完成")
        return True
    
    def _load_initial_data(self) -> None:
        \"\"\"加载初始数据\"\"\"
        # 这里可以从数据库或文件加载数据
        # 目前创建一些示例数据
        sample_data = [
"""
        
        # 添加示例数据
        for i in range(3):
            sample_item = "            {\n"
            for field in self.fields:
                if field.name == "id":
                    sample_item += f'                "id": {i + 1},\n'
                elif field.field_type == "string":
                    sample_item += f'                "{field.name}": "示例{field.name}{i + 1}",\n'
                elif field.field_type == "integer":
                    sample_item += f'                "{field.name}": {(i + 1) * 10},\n'
                elif field.field_type == "boolean":
                    sample_item += f'                "{field.name}": {str(i % 2 == 0).lower()},\n'
                elif field.field_type == "text":
                    sample_item += f'                "{field.name}": "这是示例文本内容{i + 1}",\n'
            sample_item = sample_item.rstrip(',\n') + '\n            },'
            
            if i == 0:
                class_code += sample_item + "\n"
        
        class_code += """        ]
        
        for data in sample_data:
            item = self.get_model().__class__(**data)
            self._items.append(item)
    
    def cleanup(self) -> None:
        \"\"\"清理控制器\"\"\"
        super().cleanup()
        self.logger.info(f"{self.class_name}Controller 清理完成")
    
    # 业务逻辑方法
    def search_items(self, query: str) -> List[{self.class_name}]:
        \"\"\"
        搜索项目
        
        Args:
            query: 搜索查询
            
        Returns:
            匹配的项目列表
        \"\"\"
        if not query:
            return self._items.copy()
        
        results = []
        query_lower = query.lower()
        
        for item in self._items:
"""
        
        # 添加搜索逻辑
        for field in self.fields:
            if field.field_type in ["string", "text", "email", "url", "phone"]:
                class_code += f"""            {field.name}_value = item.get_value("{field.name}")
            if {field.name}_value and query_lower in str({field.name}_value).lower():
                results.append(item)
                continue
            
"""
        
        class_code += """        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        \"\"\"
        获取统计信息
        
        Returns:
            统计信息字典
        \"\"\"
        total_count = len(self._items)
        
        stats = {
            "total_count": total_count,
            "created_today": 0,  # 可以根据created_at字段计算
        }
        
        return stats
"""
        
        return "\n".join(imports) + "\n" + class_code


class CRUDGenerator(BaseView):
    """
    CRUD生成工具
    
    提供完整的CRUD代码生成功能。
    """
    
    # 信号定义
    code_generated = Signal(str, str)  # 代码生成信号 (文件类型, 代码内容)
    
    def __init__(self, parent: QWidget = None):
        """
        初始化CRUD生成工具
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        # 字段定义
        self.fields: List[FieldDefinition] = []
        
        # 代码生成器
        self.code_generator: Optional[CodeGenerator] = None
        
        # 支持的字段类型
        self.field_types = [
            "string", "integer", "float", "boolean",
            "date", "datetime", "text", "email", "url", "phone"
        ]
        
        # 支持的数据库类型
        self.database_types = ["SQLite", "MySQL", "PostgreSQL"]
    
    def setup_ui(self) -> None:
        """设置用户界面"""
        content_widget = self.get_content_widget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 标题
        title_label = QLabel("CRUD 代码生成器")
        title_label.setStyleSheet("font-size: 24px; font-weight: 600; color: #1D1D1F; margin-bottom: 16px;")
        layout.addWidget(title_label)
        
        # 主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：配置区域
        self._create_config_area(main_splitter)
        
        # 右侧：预览区域
        self._create_preview_area(main_splitter)
        
        # 设置分割器比例
        main_splitter.setSizes([500, 700])
        layout.addWidget(main_splitter)
    
    def _create_config_area(self, splitter: QSplitter) -> None:
        """
        创建配置区域
        
        Args:
            splitter: 分割器
        """
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        config_layout.setContentsMargins(0, 0, 0, 0)
        config_layout.setSpacing(16)
        
        # 基本设置
        self._create_basic_settings(config_layout)
        
        # 字段定义
        self._create_field_definition(config_layout)
        
        # 生成按钮
        self._create_generate_buttons(config_layout)
        
        splitter.addWidget(config_widget)
    
    def _create_basic_settings(self, layout: QVBoxLayout) -> None:
        """
        创建基本设置
        
        Args:
            layout: 父布局
        """
        basic_group = QGroupBox("基本设置")
        basic_layout = QFormLayout(basic_group)
        basic_layout.setSpacing(12)
        
        # 表名
        self.table_name_input = QLineEdit()
        self.table_name_input.setPlaceholderText("例如: user, product, order")
        self.table_name_input.textChanged.connect(self._on_table_name_changed)
        basic_layout.addRow("表名:", self.table_name_input)
        
        # 数据库类型
        self.database_combo = QComboBox()
        self.database_combo.addItems(self.database_types)
        basic_layout.addRow("数据库类型:", self.database_combo)
        
        # 生成选项
        options_widget = QWidget()
        options_layout = QVBoxLayout(options_widget)
        options_layout.setContentsMargins(0, 0, 0, 0)
        
        self.generate_model_cb = QCheckBox("生成 Model")
        self.generate_model_cb.setChecked(True)
        options_layout.addWidget(self.generate_model_cb)
        
        self.generate_view_cb = QCheckBox("生成 View")
        self.generate_view_cb.setChecked(True)
        options_layout.addWidget(self.generate_view_cb)
        
        self.generate_controller_cb = QCheckBox("生成 Controller")
        self.generate_controller_cb.setChecked(True)
        options_layout.addWidget(self.generate_controller_cb)
        
        basic_layout.addRow("生成选项:", options_widget)
        
        layout.addWidget(basic_group)
    
    def _create_field_definition(self, layout: QVBoxLayout) -> None:
        """
        创建字段定义区域
        
        Args:
            layout: 父布局
        """
        field_group = QGroupBox("字段定义")
        field_layout = QVBoxLayout(field_group)
        field_layout.setSpacing(12)
        
        # 字段表格
        self.field_table = QTableWidget()
        self.field_table.setColumnCount(6)
        self.field_table.setHorizontalHeaderLabels([
            "字段名", "类型", "必填", "默认值", "最大长度", "描述"
        ])
        
        # 设置列宽
        header = self.field_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 字段名
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 类型
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 必填
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 默认值
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 最大长度
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # 描述
        
        self.field_table.setMinimumHeight(200)
        field_layout.addWidget(self.field_table)
        
        # 字段操作按钮
        field_buttons = QWidget()
        field_buttons_layout = QHBoxLayout(field_buttons)
        field_buttons_layout.setContentsMargins(0, 0, 0, 0)
        
        add_field_button = QPushButton("➕ 添加字段")
        add_field_button.clicked.connect(self._add_field)
        field_buttons_layout.addWidget(add_field_button)
        
        remove_field_button = QPushButton("➖ 删除字段")
        remove_field_button.clicked.connect(self._remove_field)
        field_buttons_layout.addWidget(remove_field_button)
        
        field_buttons_layout.addStretch()
        
        # 预设模板按钮
        template_button = QPushButton("📋 使用模板")
        template_button.clicked.connect(self._show_templates)
        field_buttons_layout.addWidget(template_button)
        
        field_layout.addWidget(field_buttons)
        
        layout.addWidget(field_group)
        
        # 添加默认字段
        self._add_default_fields()
    
    def _create_generate_buttons(self, layout: QVBoxLayout) -> None:
        """
        创建生成按钮区域
        
        Args:
            layout: 父布局
        """
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, 0)
        
        # 生成代码按钮
        generate_button = QPushButton("🚀 生成代码")
        generate_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #007AFF,
                                            stop: 1 #0056CC);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #1A8CFF,
                                            stop: 1 #0066E6);
            }
        """)
        generate_button.clicked.connect(self._generate_code)
        button_layout.addWidget(generate_button)
        
        # 导出代码按钮
        export_button = QPushButton("📤 导出代码")
        export_button.clicked.connect(self._export_code)
        button_layout.addWidget(export_button)
        
        layout.addWidget(button_widget)
    
    def _create_preview_area(self, splitter: QSplitter) -> None:
        """
        创建预览区域
        
        Args:
            splitter: 分割器
        """
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        preview_layout.setContentsMargins(0, 0, 0, 0)
        preview_layout.setSpacing(8)
        
        # 预览标题
        preview_label = QLabel("代码预览")
        preview_label.setStyleSheet("font-size: 16px; font-weight: 600; color: #1D1D1F;")
        preview_layout.addWidget(preview_label)
        
        # 预览标签页
        self.preview_tabs = QTabWidget()
        self.preview_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #E5E5EA;
                border-radius: 6px;
                background-color: white;
            }
            QTabBar::tab {
                background: #F8F8F8;
                border: 1px solid #E5E5EA;
                border-bottom: none;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom: 1px solid white;
            }
        """)
        
        # Model预览
        self.model_preview = QTextEdit()
        self.model_preview.setReadOnly(True)
        self.model_preview.setFont(QFont("Consolas", 10))
        self.model_highlighter = PythonSyntaxHighlighter(self.model_preview.document())
        self.preview_tabs.addTab(self.model_preview, "📊 Model")
        
        # View预览
        self.view_preview = QTextEdit()
        self.view_preview.setReadOnly(True)
        self.view_preview.setFont(QFont("Consolas", 10))
        self.view_highlighter = PythonSyntaxHighlighter(self.view_preview.document())
        self.preview_tabs.addTab(self.view_preview, "🖼️ View")
        
        # Controller预览
        self.controller_preview = QTextEdit()
        self.controller_preview.setReadOnly(True)
        self.controller_preview.setFont(QFont("Consolas", 10))
        self.controller_highlighter = PythonSyntaxHighlighter(self.controller_preview.document())
        self.preview_tabs.addTab(self.controller_preview, "🎮 Controller")
        
        preview_layout.addWidget(self.preview_tabs)
        
        splitter.addWidget(preview_widget)
    
    def _add_default_fields(self) -> None:
        """添加默认字段"""
        default_fields = [
            FieldDefinition("id", "integer", False, "", None, [], "主键ID"),
            FieldDefinition("name", "string", True, "", 100, [], "名称"),
            FieldDefinition("created_at", "datetime", False, "", None, [], "创建时间"),
            FieldDefinition("updated_at", "datetime", False, "", None, [], "更新时间"),
        ]
        
        for field in default_fields:
            self._add_field_to_table(field)
    
    def _add_field(self) -> None:
        """添加字段"""
        field = FieldDefinition("new_field", "string", False, "", None, [], "")
        self._add_field_to_table(field)
    
    def _add_field_to_table(self, field: FieldDefinition) -> None:
        """
        添加字段到表格
        
        Args:
            field: 字段定义
        """
        row = self.field_table.rowCount()
        self.field_table.insertRow(row)
        
        # 字段名
        name_item = QTableWidgetItem(field.name)
        self.field_table.setItem(row, 0, name_item)
        
        # 类型下拉框
        type_combo = QComboBox()
        type_combo.addItems(self.field_types)
        type_combo.setCurrentText(field.field_type)
        self.field_table.setCellWidget(row, 1, type_combo)
        
        # 必填复选框
        required_cb = QCheckBox()
        required_cb.setChecked(field.required)
        required_widget = QWidget()
        required_layout = QHBoxLayout(required_widget)
        required_layout.addWidget(required_cb)
        required_layout.setAlignment(Qt.AlignCenter)
        required_layout.setContentsMargins(0, 0, 0, 0)
        self.field_table.setCellWidget(row, 2, required_widget)
        
        # 默认值
        default_item = QTableWidgetItem(field.default_value)
        self.field_table.setItem(row, 3, default_item)
        
        # 最大长度
        max_length_item = QTableWidgetItem(str(field.max_length) if field.max_length else "")
        self.field_table.setItem(row, 4, max_length_item)
        
        # 描述
        desc_item = QTableWidgetItem(field.description)
        self.field_table.setItem(row, 5, desc_item)
    
    def _remove_field(self) -> None:
        """删除字段"""
        current_row = self.field_table.currentRow()
        if current_row >= 0:
            self.field_table.removeRow(current_row)
    
    def _show_templates(self) -> None:
        """显示模板对话框"""
        QMessageBox.information(self, "模板功能", "模板功能正在开发中...")
    
    def _on_table_name_changed(self, text: str) -> None:
        """
        表名变更处理
        
        Args:
            text: 新表名
        """
        # 可以在这里添加表名验证逻辑
        pass
    
    def _collect_fields(self) -> List[FieldDefinition]:
        """
        收集字段定义
        
        Returns:
            字段定义列表
        """
        fields = []
        
        for row in range(self.field_table.rowCount()):
            # 字段名
            name_item = self.field_table.item(row, 0)
            name = name_item.text() if name_item else ""
            
            # 类型
            type_combo = self.field_table.cellWidget(row, 1)
            field_type = type_combo.currentText() if type_combo else "string"
            
            # 必填
            required_widget = self.field_table.cellWidget(row, 2)
            required_cb = required_widget.findChild(QCheckBox) if required_widget else None
            required = required_cb.isChecked() if required_cb else False
            
            # 默认值
            default_item = self.field_table.item(row, 3)
            default_value = default_item.text() if default_item else ""
            
            # 最大长度
            max_length_item = self.field_table.item(row, 4)
            max_length_text = max_length_item.text() if max_length_item else ""
            max_length = int(max_length_text) if max_length_text.isdigit() else None
            
            # 描述
            desc_item = self.field_table.item(row, 5)
            description = desc_item.text() if desc_item else ""
            
            if name:  # 只添加有名称的字段
                field = FieldDefinition(
                    name=name,
                    field_type=field_type,
                    required=required,
                    default_value=default_value,
                    max_length=max_length,
                    description=description
                )
                fields.append(field)
        
        return fields
    
    def _generate_code(self) -> None:
        """生成代码"""
        table_name = self.table_name_input.text().strip()
        if not table_name:
            QMessageBox.warning(self, "警告", "请输入表名")
            return
        
        fields = self._collect_fields()
        if not fields:
            QMessageBox.warning(self, "警告", "请至少定义一个字段")
            return
        
        # 创建代码生成器
        self.code_generator = CodeGenerator(table_name, fields)
        
        # 生成代码
        try:
            if self.generate_model_cb.isChecked():
                model_code = self.code_generator.generate_model()
                self.model_preview.setPlainText(model_code)
                self.code_generated.emit("model", model_code)
            
            if self.generate_view_cb.isChecked():
                view_code = self.code_generator.generate_view()
                self.view_preview.setPlainText(view_code)
                self.code_generated.emit("view", view_code)
            
            if self.generate_controller_cb.isChecked():
                controller_code = self.code_generator.generate_controller()
                self.controller_preview.setPlainText(controller_code)
                self.code_generated.emit("controller", controller_code)
            
            QMessageBox.information(self, "成功", "代码生成完成！")
            self.logger.info(f"CRUD代码生成完成: {table_name}")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"代码生成失败: {e}")
            self.logger.error(f"代码生成失败: {e}")
    
    def _export_code(self) -> None:
        """导出代码"""
        if not self.code_generator:
            QMessageBox.warning(self, "警告", "请先生成代码")
            return
        
        # 选择导出目录
        export_dir = QFileDialog.getExistingDirectory(self, "选择导出目录")
        if not export_dir:
            return
        
        export_path = Path(export_dir)
        table_name = self.code_generator.table_name
        
        try:
            # 导出Model
            if self.generate_model_cb.isChecked() and self.model_preview.toPlainText():
                model_file = export_path / f"{table_name}_model.py"
                with open(model_file, 'w', encoding='utf-8') as f:
                    f.write(self.model_preview.toPlainText())
            
            # 导出View
            if self.generate_view_cb.isChecked() and self.view_preview.toPlainText():
                view_file = export_path / f"{table_name}_view.py"
                with open(view_file, 'w', encoding='utf-8') as f:
                    f.write(self.view_preview.toPlainText())
            
            # 导出Controller
            if self.generate_controller_cb.isChecked() and self.controller_preview.toPlainText():
                controller_file = export_path / f"{table_name}_controller.py"
                with open(controller_file, 'w', encoding='utf-8') as f:
                    f.write(self.controller_preview.toPlainText())
            
            QMessageBox.information(self, "导出成功", f"代码已导出到: {export_path}")
            self.logger.info(f"代码已导出到: {export_path}")
            
        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"导出失败: {e}")
            self.logger.error(f"代码导出失败: {e}")
    
    def cleanup(self) -> None:
        """清理资源"""
        super().cleanup()