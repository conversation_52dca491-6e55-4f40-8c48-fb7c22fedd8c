/*
日间主题样式表
实现现代化的macOS风格设计，包含圆角、阴影、渐变等视觉效果
*/

/* 全局样式 */
* {
    font-family: 'Microsoft YaHei UI', 'Segoe UI', 'SF Pro Display', sans-serif;
    font-size: 13px;
    outline: none;
}

/* 主窗口 */
QMainWindow {
    background-color: ${background};
    border-radius: 8px;
}

/* 无边框窗口容器 */
QWidget#MainContainer {
    background-color: ${background};
    border-radius: 8px;
    border: 1px solid ${border};
}

/* 自定义标题栏 */
QWidget#TitleBar {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #FFFFFF,
                                stop: 1 #F8F8F8);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    border-bottom: 1px solid ${border};
    min-height: 44px;
    max-height: 44px;
}

/* 标题栏按钮 */
QPushButton#TitleBarButton {
    background-color: transparent;
    border: none;
    border-radius: 6px;
    width: 12px;
    height: 12px;
    margin: 2px;
}

QPushButton#CloseButton {
    background-color: #FF5F57;
    border: 1px solid #E0443E;
}

QPushButton#CloseButton:hover {
    background-color: #FF6B63;
}

QPushButton#MinimizeButton {
    background-color: #FFBD2E;
    border: 1px solid #DEA123;
}

QPushButton#MinimizeButton:hover {
    background-color: #FFC441;
}

QPushButton#MaximizeButton {
    background-color: #28CA42;
    border: 1px solid #1AAB29;
}

QPushButton#MaximizeButton:hover {
    background-color: #30D748;
}

/* 标题文本 */
QLabel#TitleLabel {
    color: ${text};
    font-size: 14px;
    font-weight: 600;
    background-color: transparent;
}

/* 侧边栏 */
QWidget#Sidebar {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                stop: 0 #FAFAFA,
                                stop: 1 #F5F5F5);
    border-right: 1px solid ${border};
    border-bottom-left-radius: 8px;
}

/* 侧边栏按钮 */
QPushButton#SidebarButton {
    background-color: transparent;
    color: ${text};
    border: none;
    border-radius: 6px;
    padding: 12px 16px;
    text-align: left;
    font-size: 13px;
    font-weight: 500;
    margin: 2px 8px;
}

QPushButton#SidebarButton:hover {
    background-color: rgba(0, 122, 255, 0.1);
    color: ${primary};
}

QPushButton#SidebarButton:pressed {
    background-color: rgba(0, 122, 255, 0.2);
}

QPushButton#SidebarButton:checked {
    background-color: ${primary};
    color: white;
}

/* 侧边栏图标 */
QLabel#SidebarIcon {
    background-color: transparent;
    color: ${text_secondary};
    font-size: 16px;
    min-width: 20px;
    max-width: 20px;
}

/* 主内容区域 */
QWidget#ContentArea {
    background-color: ${background};
    border-bottom-right-radius: 8px;
}

/* 通用按钮 */
QPushButton {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 ${primary},
                                stop: 1 #0056CC);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 13px;
    font-weight: 500;
    min-height: 20px;
}

QPushButton:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #1A8CFF,
                                stop: 1 #0066E6);
}

QPushButton:pressed {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #0066E6,
                                stop: 1 #004BB8);
}

QPushButton:disabled {
    background-color: ${text_secondary};
    color: #FFFFFF;
}

/* 次要按钮 */
QPushButton#SecondaryButton {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #FFFFFF,
                                stop: 1 #F8F8F8);
    color: ${text};
    border: 1px solid ${border};
}

QPushButton#SecondaryButton:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #F8F8F8,
                                stop: 1 #F0F0F0);
    border-color: ${primary};
}

QPushButton#SecondaryButton:pressed {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #F0F0F0,
                                stop: 1 #E8E8E8);
}

/* 输入框 */
QLineEdit {
    background-color: ${surface};
    color: ${text};
    border: 1px solid ${border};
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
    selection-background-color: ${primary};
    selection-color: white;
}

QLineEdit:focus {
    border-color: ${primary};
    background-color: #FFFFFF;
}

QLineEdit:disabled {
    background-color: #F8F8F8;
    color: ${text_secondary};
}

/* 文本区域 */
QTextEdit {
    background-color: ${surface};
    color: ${text};
    border: 1px solid ${border};
    border-radius: 6px;
    padding: 8px;
    font-size: 13px;
    selection-background-color: ${primary};
    selection-color: white;
}

QTextEdit:focus {
    border-color: ${primary};
}

/* 下拉框 */
QComboBox {
    background-color: ${surface};
    color: ${text};
    border: 1px solid ${border};
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 13px;
    min-height: 20px;
}

QComboBox:hover {
    border-color: ${primary};
}

QComboBox:focus {
    border-color: ${primary};
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzhFOEU5MyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
    width: 12px;
    height: 8px;
}

QComboBox QAbstractItemView {
    background-color: ${surface};
    border: 1px solid ${border};
    border-radius: 6px;
    selection-background-color: ${primary};
    selection-color: white;
    padding: 4px;
}

/* 列表视图 */
QListView {
    background-color: ${surface};
    color: ${text};
    border: 1px solid ${border};
    border-radius: 6px;
    padding: 4px;
    selection-background-color: ${primary};
    selection-color: white;
}

QListView::item {
    padding: 8px;
    border-radius: 4px;
    margin: 1px;
}

QListView::item:hover {
    background-color: rgba(0, 122, 255, 0.1);
}

QListView::item:selected {
    background-color: ${primary};
    color: white;
}

/* 表格视图 */
QTableView {
    background-color: ${surface};
    color: ${text};
    border: 1px solid ${border};
    border-radius: 6px;
    gridline-color: ${border};
    selection-background-color: ${primary};
    selection-color: white;
}

QTableView::item {
    padding: 8px;
    border-bottom: 1px solid ${border};
}

QTableView::item:hover {
    background-color: rgba(0, 122, 255, 0.1);
}

QTableView::item:selected {
    background-color: ${primary};
    color: white;
}

QHeaderView::section {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #FFFFFF,
                                stop: 1 #F8F8F8);
    color: ${text};
    border: none;
    border-bottom: 1px solid ${border};
    border-right: 1px solid ${border};
    padding: 8px;
    font-weight: 600;
}

/* 滚动条 */
QScrollBar:vertical {
    background-color: transparent;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: rgba(0, 0, 0, 0.3);
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: transparent;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: rgba(0, 0, 0, 0.3);
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* 标签页 */
QTabWidget::pane {
    background-color: ${surface};
    border: 1px solid ${border};
    border-radius: 6px;
    top: -1px;
}

QTabBar::tab {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #FFFFFF,
                                stop: 1 #F8F8F8);
    color: ${text};
    border: 1px solid ${border};
    border-bottom: none;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    padding: 8px 16px;
    margin-right: 2px;
}

QTabBar::tab:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #F8F8F8,
                                stop: 1 #F0F0F0);
}

QTabBar::tab:selected {
    background-color: ${surface};
    border-bottom: 1px solid ${surface};
}

/* 进度条 */
QProgressBar {
    background-color: #E5E5EA;
    border: none;
    border-radius: 4px;
    height: 8px;
    text-align: center;
}

QProgressBar::chunk {
    background-color: ${primary};
    border-radius: 4px;
}

/* 滑块 */
QSlider::groove:horizontal {
    background-color: #E5E5EA;
    height: 4px;
    border-radius: 2px;
}

QSlider::handle:horizontal {
    background-color: ${primary};
    border: 2px solid white;
    width: 16px;
    height: 16px;
    border-radius: 8px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background-color: #1A8CFF;
}

/* 复选框 */
QCheckBox {
    color: ${text};
    font-size: 13px;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid ${border};
    border-radius: 3px;
    background-color: ${surface};
}

QCheckBox::indicator:hover {
    border-color: ${primary};
}

QCheckBox::indicator:checked {
    background-color: ${primary};
    border-color: ${primary};
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=);
}

/* 单选框 */
QRadioButton {
    color: ${text};
    font-size: 13px;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid ${border};
    border-radius: 8px;
    background-color: ${surface};
}

QRadioButton::indicator:hover {
    border-color: ${primary};
}

QRadioButton::indicator:checked {
    background-color: ${primary};
    border-color: ${primary};
}

QRadioButton::indicator:checked::after {
    content: "";
    width: 6px;
    height: 6px;
    border-radius: 3px;
    background-color: white;
    margin: 4px;
}

/* 分组框 */
QGroupBox {
    color: ${text};
    border: 1px solid ${border};
    border-radius: 6px;
    margin-top: 12px;
    font-weight: 600;
    padding-top: 8px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 12px;
    padding: 0 8px 0 8px;
    background-color: ${background};
}

/* 状态栏 */
QStatusBar {
    background-color: ${background};
    color: ${text_secondary};
    border-top: 1px solid ${border};
    font-size: 12px;
    padding: 4px;
}

/* 工具栏 */
QToolBar {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #FFFFFF,
                                stop: 1 #F8F8F8);
    border-bottom: 1px solid ${border};
    spacing: 4px;
    padding: 4px;
}

QToolButton {
    background-color: transparent;
    color: ${text};
    border: none;
    border-radius: 4px;
    padding: 6px;
    font-size: 13px;
}

QToolButton:hover {
    background-color: rgba(0, 122, 255, 0.1);
}

QToolButton:pressed {
    background-color: rgba(0, 122, 255, 0.2);
}

/* 菜单 */
QMenuBar {
    background-color: transparent;
    color: ${text};
    border: none;
    font-size: 13px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 12px;
    border-radius: 4px;
}

QMenuBar::item:hover {
    background-color: rgba(0, 122, 255, 0.1);
}

QMenu {
    background-color: ${surface};
    color: ${text};
    border: 1px solid ${border};
    border-radius: 6px;
    padding: 4px;
}

QMenu::item {
    padding: 8px 16px;
    border-radius: 4px;
}

QMenu::item:hover {
    background-color: ${primary};
    color: white;
}

QMenu::separator {
    height: 1px;
    background-color: ${border};
    margin: 4px 8px;
}

/* 提示框 */
QToolTip {
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 12px;
}

/* 动画效果 */
QPushButton, QLineEdit, QComboBox, QListView::item, QTableView::item {
    transition: all 0.2s ease-in-out;
}

/* 阴影效果（通过边框模拟） */
QWidget#ShadowContainer {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: ${background};
}