"""
设置界面

实现应用程序设置功能，包括：
- 主题选择
- 语言切换
- 字体大小调整
- 配置持久化存储
- 系统参数配置
"""

import logging
import json
from typing import Dict, Any, Optional
from pathlib import Path

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QPushButton, QLineEdit, QComboBox,
                               QCheckBox, QSlider, QSpinBox, QGroupBox,
                               QTabWidget, QScrollArea, QFrame, QFileDialog,
                               QMessageBox, QColorDialog, QFontDialog)
from PySide6.QtCore import Qt, Signal, QSettings
from PySide6.QtGui import QFont, QColor, QPalette

from .base_view import BaseView


class SettingItem(QWidget):
    """
    设置项组件
    
    通用的设置项界面组件。
    """
    
    value_changed = Signal(str, object)  # 值变更信号 (设置键, 新值)
    
    def __init__(self, key: str, title: str, description: str = "", 
                 widget_type: str = "text", options: list = None, 
                 default_value: Any = None, parent: QWidget = None):
        """
        初始化设置项
        
        Args:
            key: 设置键
            title: 标题
            description: 描述
            widget_type: 组件类型 (text, combo, checkbox, slider, spin, color, font)
            options: 选项列表（用于combo类型）
            default_value: 默认值
            parent: 父组件
        """
        super().__init__(parent)
        
        self.key = key
        self.title = title
        self.description = description
        self.widget_type = widget_type
        self.options = options or []
        self.default_value = default_value
        
        self._setup_ui()
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 标题和描述
        title_label = QLabel(self.title)
        title_label.setStyleSheet("font-weight: 600; font-size: 14px; color: #1D1D1F;")
        layout.addWidget(title_label)
        
        if self.description:
            desc_label = QLabel(self.description)
            desc_label.setStyleSheet("font-size: 12px; color: #8E8E93;")
            desc_label.setWordWrap(True)
            layout.addWidget(desc_label)
        
        # 控制组件
        self._create_control_widget()
        layout.addWidget(self.control_widget)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet("color: #E5E5EA;")
        layout.addWidget(separator)
    
    def _create_control_widget(self) -> None:
        """创建控制组件"""
        if self.widget_type == "text":
            self.control_widget = QLineEdit()
            if self.default_value:
                self.control_widget.setText(str(self.default_value))
            self.control_widget.textChanged.connect(
                lambda text: self.value_changed.emit(self.key, text)
            )
        
        elif self.widget_type == "combo":
            self.control_widget = QComboBox()
            for option in self.options:
                if isinstance(option, tuple):
                    self.control_widget.addItem(option[0], option[1])
                else:
                    self.control_widget.addItem(str(option), option)
            
            if self.default_value is not None:
                index = self.control_widget.findData(self.default_value)
                if index >= 0:
                    self.control_widget.setCurrentIndex(index)
            
            self.control_widget.currentIndexChanged.connect(
                lambda: self.value_changed.emit(self.key, self.control_widget.currentData())
            )
        
        elif self.widget_type == "checkbox":
            self.control_widget = QCheckBox()
            if self.default_value is not None:
                self.control_widget.setChecked(bool(self.default_value))
            self.control_widget.toggled.connect(
                lambda checked: self.value_changed.emit(self.key, checked)
            )
        
        elif self.widget_type == "slider":
            container = QWidget()
            layout = QHBoxLayout(container)
            layout.setContentsMargins(0, 0, 0, 0)
            
            self.slider = QSlider(Qt.Horizontal)
            self.slider.setMinimum(self.options[0] if self.options else 0)
            self.slider.setMaximum(self.options[1] if len(self.options) > 1 else 100)
            if self.default_value is not None:
                self.slider.setValue(int(self.default_value))
            
            self.value_label = QLabel(str(self.slider.value()))
            self.value_label.setMinimumWidth(40)
            
            self.slider.valueChanged.connect(self._on_slider_changed)
            
            layout.addWidget(self.slider, 1)
            layout.addWidget(self.value_label)
            
            self.control_widget = container
        
        elif self.widget_type == "spin":
            self.control_widget = QSpinBox()
            self.control_widget.setMinimum(self.options[0] if self.options else 0)
            self.control_widget.setMaximum(self.options[1] if len(self.options) > 1 else 999)
            if self.default_value is not None:
                self.control_widget.setValue(int(self.default_value))
            self.control_widget.valueChanged.connect(
                lambda value: self.value_changed.emit(self.key, value)
            )
        
        elif self.widget_type == "color":
            container = QWidget()
            layout = QHBoxLayout(container)
            layout.setContentsMargins(0, 0, 0, 0)
            
            self.color_button = QPushButton()
            self.color_button.setFixedSize(40, 30)
            self.current_color = QColor(self.default_value) if self.default_value else QColor("#007AFF")
            self._update_color_button()
            self.color_button.clicked.connect(self._choose_color)
            
            self.color_label = QLabel(self.current_color.name())
            
            layout.addWidget(self.color_button)
            layout.addWidget(self.color_label)
            layout.addStretch()
            
            self.control_widget = container
        
        elif self.widget_type == "font":
            container = QWidget()
            layout = QHBoxLayout(container)
            layout.setContentsMargins(0, 0, 0, 0)
            
            self.font_button = QPushButton("选择字体")
            self.current_font = QFont(self.default_value) if self.default_value else QFont()
            self.font_button.clicked.connect(self._choose_font)
            
            self.font_label = QLabel(f"{self.current_font.family()}, {self.current_font.pointSize()}pt")
            
            layout.addWidget(self.font_button)
            layout.addWidget(self.font_label, 1)
            
            self.control_widget = container
        
        else:
            self.control_widget = QLabel("不支持的设置类型")
    
    def _on_slider_changed(self, value: int) -> None:
        """滑块值变更处理"""
        self.value_label.setText(str(value))
        self.value_changed.emit(self.key, value)
    
    def _update_color_button(self) -> None:
        """更新颜色按钮"""
        self.color_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.current_color.name()};
                border: 1px solid #D1D1D6;
                border-radius: 4px;
            }}
        """)
    
    def _choose_color(self) -> None:
        """选择颜色"""
        color = QColorDialog.getColor(self.current_color, self, "选择颜色")
        if color.isValid():
            self.current_color = color
            self._update_color_button()
            self.color_label.setText(color.name())
            self.value_changed.emit(self.key, color.name())
    
    def _choose_font(self) -> None:
        """选择字体"""
        font, ok = QFontDialog.getFont(self.current_font, self, "选择字体")
        if ok:
            self.current_font = font
            self.font_label.setText(f"{font.family()}, {font.pointSize()}pt")
            self.value_changed.emit(self.key, font.toString())
    
    def get_value(self) -> Any:
        """获取当前值"""
        if self.widget_type == "text":
            return self.control_widget.text()
        elif self.widget_type == "combo":
            return self.control_widget.currentData()
        elif self.widget_type == "checkbox":
            return self.control_widget.isChecked()
        elif self.widget_type == "slider":
            return self.slider.value()
        elif self.widget_type == "spin":
            return self.control_widget.value()
        elif self.widget_type == "color":
            return self.current_color.name()
        elif self.widget_type == "font":
            return self.current_font.toString()
        return None
    
    def set_value(self, value: Any) -> None:
        """设置值"""
        if self.widget_type == "text":
            self.control_widget.setText(str(value))
        elif self.widget_type == "combo":
            index = self.control_widget.findData(value)
            if index >= 0:
                self.control_widget.setCurrentIndex(index)
        elif self.widget_type == "checkbox":
            self.control_widget.setChecked(bool(value))
        elif self.widget_type == "slider":
            self.slider.setValue(int(value))
        elif self.widget_type == "spin":
            self.control_widget.setValue(int(value))
        elif self.widget_type == "color":
            self.current_color = QColor(value)
            self._update_color_button()
            self.color_label.setText(value)
        elif self.widget_type == "font":
            self.current_font.fromString(value)
            self.font_label.setText(f"{self.current_font.family()}, {self.current_font.pointSize()}pt")


class Settings(BaseView):
    """
    设置界面
    
    应用程序的设置管理界面。
    """
    
    # 信号定义
    setting_changed = Signal(str, object)  # 设置变更信号
    theme_change_requested = Signal(str)  # 主题变更请求信号
    language_change_requested = Signal(str)  # 语言变更请求信号
    
    def __init__(self, parent: QWidget = None):
        """
        初始化设置界面
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        # 设置存储
        self.settings = QSettings()
        
        # 设置项
        self.setting_items: Dict[str, SettingItem] = {}
        
        # 设置定义
        self.setting_definitions = self._get_setting_definitions()
    
    def setup_ui(self) -> None:
        """设置用户界面"""
        content_widget = self.get_content_widget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 标题
        title_label = QLabel("应用程序设置")
        title_label.setStyleSheet("font-size: 24px; font-weight: 600; color: #1D1D1F; margin-bottom: 16px;")
        layout.addWidget(title_label)
        
        # 标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #E5E5EA;
                border-radius: 6px;
                background-color: white;
            }
            QTabBar::tab {
                background: #F8F8F8;
                border: 1px solid #E5E5EA;
                border-bottom: none;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background: #F0F0F0;
            }
        """)
        
        # 创建设置标签页
        self._create_appearance_tab()
        self._create_general_tab()
        self._create_advanced_tab()
        self._create_about_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        self._create_button_area(layout)
        
        # 加载设置
        self._load_settings()
    
    def _get_setting_definitions(self) -> Dict[str, Dict]:
        """获取设置定义"""
        return {
            # 外观设置
            "theme": {
                "title": "主题",
                "description": "选择应用程序的外观主题",
                "type": "combo",
                "options": [("日间模式", "light"), ("夜间模式", "dark")],
                "default": "light",
                "tab": "appearance"
            },
            "font_family": {
                "title": "字体",
                "description": "选择应用程序的字体",
                "type": "font",
                "default": "Microsoft YaHei UI",
                "tab": "appearance"
            },
            "font_size": {
                "title": "字体大小",
                "description": "调整应用程序的字体大小",
                "type": "slider",
                "options": [8, 24],
                "default": 12,
                "tab": "appearance"
            },
            "primary_color": {
                "title": "主色调",
                "description": "选择应用程序的主色调",
                "type": "color",
                "default": "#007AFF",
                "tab": "appearance"
            },
            
            # 通用设置
            "language": {
                "title": "语言",
                "description": "选择应用程序的显示语言",
                "type": "combo",
                "options": [("简体中文", "zh_CN"), ("English", "en_US")],
                "default": "zh_CN",
                "tab": "general"
            },
            "auto_save": {
                "title": "自动保存",
                "description": "启用自动保存功能",
                "type": "checkbox",
                "default": True,
                "tab": "general"
            },
            "auto_save_interval": {
                "title": "自动保存间隔（分钟）",
                "description": "设置自动保存的时间间隔",
                "type": "spin",
                "options": [1, 60],
                "default": 5,
                "tab": "general"
            },
            "startup_page": {
                "title": "启动页面",
                "description": "选择应用程序启动时显示的页面",
                "type": "combo",
                "options": [("仪表盘", "dashboard"), ("日志查看器", "log_viewer"), ("设置", "settings")],
                "default": "dashboard",
                "tab": "general"
            },
            
            # 高级设置
            "debug_mode": {
                "title": "调试模式",
                "description": "启用调试模式（显示详细日志）",
                "type": "checkbox",
                "default": False,
                "tab": "advanced"
            },
            "log_level": {
                "title": "日志级别",
                "description": "设置应用程序的日志记录级别",
                "type": "combo",
                "options": [("调试", "DEBUG"), ("信息", "INFO"), ("警告", "WARNING"), ("错误", "ERROR")],
                "default": "INFO",
                "tab": "advanced"
            },
            "max_log_files": {
                "title": "最大日志文件数",
                "description": "设置保留的最大日志文件数量",
                "type": "spin",
                "options": [1, 100],
                "default": 10,
                "tab": "advanced"
            },
            "performance_monitoring": {
                "title": "性能监控",
                "description": "启用性能监控功能",
                "type": "checkbox",
                "default": True,
                "tab": "advanced"
            }
        }
    
    def _create_appearance_tab(self) -> None:
        """创建外观设置标签页"""
        tab = QWidget()
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 添加外观设置项
        for key, definition in self.setting_definitions.items():
            if definition.get("tab") == "appearance":
                setting_item = SettingItem(
                    key=key,
                    title=definition["title"],
                    description=definition["description"],
                    widget_type=definition["type"],
                    options=definition.get("options"),
                    default_value=definition.get("default")
                )
                setting_item.value_changed.connect(self._on_setting_changed)
                self.setting_items[key] = setting_item
                layout.addWidget(setting_item)
        
        layout.addStretch()
        scroll_area.setWidget(content)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)
        
        self.tab_widget.addTab(tab, "🎨 外观")
    
    def _create_general_tab(self) -> None:
        """创建通用设置标签页"""
        tab = QWidget()
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 添加通用设置项
        for key, definition in self.setting_definitions.items():
            if definition.get("tab") == "general":
                setting_item = SettingItem(
                    key=key,
                    title=definition["title"],
                    description=definition["description"],
                    widget_type=definition["type"],
                    options=definition.get("options"),
                    default_value=definition.get("default")
                )
                setting_item.value_changed.connect(self._on_setting_changed)
                self.setting_items[key] = setting_item
                layout.addWidget(setting_item)
        
        layout.addStretch()
        scroll_area.setWidget(content)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)
        
        self.tab_widget.addTab(tab, "⚙️ 通用")
    
    def _create_advanced_tab(self) -> None:
        """创建高级设置标签页"""
        tab = QWidget()
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 警告提示
        warning_label = QLabel("⚠️ 高级设置可能影响应用程序的稳定性，请谨慎修改。")
        warning_label.setStyleSheet("""
            QLabel {
                background-color: #FFF3CD;
                color: #856404;
                border: 1px solid #FFEAA7;
                border-radius: 6px;
                padding: 12px;
                font-weight: 500;
            }
        """)
        layout.addWidget(warning_label)
        
        # 添加高级设置项
        for key, definition in self.setting_definitions.items():
            if definition.get("tab") == "advanced":
                setting_item = SettingItem(
                    key=key,
                    title=definition["title"],
                    description=definition["description"],
                    widget_type=definition["type"],
                    options=definition.get("options"),
                    default_value=definition.get("default")
                )
                setting_item.value_changed.connect(self._on_setting_changed)
                self.setting_items[key] = setting_item
                layout.addWidget(setting_item)
        
        layout.addStretch()
        scroll_area.setWidget(content)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)
        
        self.tab_widget.addTab(tab, "🔧 高级")
    
    def _create_about_tab(self) -> None:
        """创建关于标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(20)
        
        # 应用程序图标和名称
        app_info = QWidget()
        app_layout = QVBoxLayout(app_info)
        app_layout.setAlignment(Qt.AlignCenter)
        
        app_name = QLabel("PySide6开发框架")
        app_name.setStyleSheet("font-size: 28px; font-weight: 600; color: #1D1D1F;")
        app_name.setAlignment(Qt.AlignCenter)
        app_layout.addWidget(app_name)
        
        app_version = QLabel("版本 1.0.0")
        app_version.setStyleSheet("font-size: 16px; color: #8E8E93;")
        app_version.setAlignment(Qt.AlignCenter)
        app_layout.addWidget(app_version)
        
        app_desc = QLabel("一个现代化、模块化的桌面应用程序开发框架")
        app_desc.setStyleSheet("font-size: 14px; color: #8E8E93; margin-top: 8px;")
        app_desc.setAlignment(Qt.AlignCenter)
        app_desc.setWordWrap(True)
        app_layout.addWidget(app_desc)
        
        layout.addWidget(app_info)
        
        # 系统信息
        system_group = QGroupBox("系统信息")
        system_layout = QGridLayout(system_group)
        
        import sys
        import platform
        from PySide6 import __version__ as pyside_version
        
        system_info = [
            ("操作系统", platform.system() + " " + platform.release()),
            ("Python版本", sys.version.split()[0]),
            ("PySide6版本", pyside_version),
            ("架构", platform.machine()),
        ]
        
        for i, (label, value) in enumerate(system_info):
            label_widget = QLabel(label + ":")
            label_widget.setStyleSheet("font-weight: 600;")
            value_widget = QLabel(value)
            
            system_layout.addWidget(label_widget, i, 0)
            system_layout.addWidget(value_widget, i, 1)
        
        layout.addWidget(system_group)
        
        # 版权信息
        copyright_label = QLabel("© 2023 PySide6框架开发团队. 保留所有权利.")
        copyright_label.setStyleSheet("font-size: 12px; color: #8E8E93;")
        copyright_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(copyright_label)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "ℹ️ 关于")
    
    def _create_button_area(self, layout: QVBoxLayout) -> None:
        """
        创建按钮区域
        
        Args:
            layout: 父布局
        """
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 16, 0, 0)
        
        # 导入/导出按钮
        import_button = QPushButton("📥 导入设置")
        import_button.clicked.connect(self._import_settings)
        button_layout.addWidget(import_button)
        
        export_button = QPushButton("📤 导出设置")
        export_button.clicked.connect(self._export_settings)
        button_layout.addWidget(export_button)
        
        button_layout.addStretch()
        
        # 重置按钮
        reset_button = QPushButton("🔄 重置为默认")
        reset_button.clicked.connect(self._reset_to_defaults)
        button_layout.addWidget(reset_button)
        
        # 应用按钮
        apply_button = QPushButton("✅ 应用设置")
        apply_button.clicked.connect(self._apply_settings)
        button_layout.addWidget(apply_button)
        
        layout.addWidget(button_widget)
    
    def _on_setting_changed(self, key: str, value: Any) -> None:
        """
        设置变更处理
        
        Args:
            key: 设置键
            value: 新值
        """
        # 保存到QSettings
        self.settings.setValue(key, value)
        
        # 发送信号
        self.setting_changed.emit(key, value)
        
        # 特殊处理
        if key == "theme":
            self.theme_change_requested.emit(value)
        elif key == "language":
            self.language_change_requested.emit(value)
        
        self.logger.debug(f"设置已变更: {key} = {value}")
    
    def _load_settings(self) -> None:
        """加载设置"""
        for key, item in self.setting_items.items():
            definition = self.setting_definitions.get(key, {})
            default_value = definition.get("default")
            
            # 从QSettings加载值
            value = self.settings.value(key, default_value)
            
            # 类型转换
            if definition.get("type") == "checkbox":
                value = bool(value) if isinstance(value, str) else value
            elif definition.get("type") in ["slider", "spin"]:
                value = int(value) if value is not None else default_value
            
            item.set_value(value)
        
        self.logger.info("设置已加载")
    
    def _apply_settings(self) -> None:
        """应用设置"""
        # 同步设置到磁盘
        self.settings.sync()
        
        QMessageBox.information(self, "设置已应用", "所有设置已成功应用并保存。")
        self.logger.info("设置已应用")
    
    def _reset_to_defaults(self) -> None:
        """重置为默认设置"""
        reply = QMessageBox.question(self, "确认重置", 
                                   "确定要将所有设置重置为默认值吗？\n此操作无法撤销。",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            # 清除所有设置
            self.settings.clear()
            
            # 重新加载默认值
            for key, item in self.setting_items.items():
                definition = self.setting_definitions.get(key, {})
                default_value = definition.get("default")
                item.set_value(default_value)
                
                # 触发变更信号
                self._on_setting_changed(key, default_value)
            
            QMessageBox.information(self, "重置完成", "所有设置已重置为默认值。")
            self.logger.info("设置已重置为默认值")
    
    def _export_settings(self) -> None:
        """导出设置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出设置", "settings.json", "JSON文件 (*.json)"
        )
        
        if file_path:
            try:
                # 收集所有设置
                settings_data = {}
                for key in self.setting_items.keys():
                    value = self.settings.value(key)
                    if value is not None:
                        settings_data[key] = value
                
                # 写入文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(settings_data, f, ensure_ascii=False, indent=2)
                
                QMessageBox.information(self, "导出成功", f"设置已导出到: {file_path}")
                self.logger.info(f"设置已导出到: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"导出设置失败: {e}")
                self.logger.error(f"导出设置失败: {e}")
    
    def _import_settings(self) -> None:
        """导入设置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入设置", "", "JSON文件 (*.json)"
        )
        
        if file_path:
            try:
                # 读取文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    settings_data = json.load(f)
                
                # 应用设置
                for key, value in settings_data.items():
                    if key in self.setting_items:
                        self.settings.setValue(key, value)
                        self.setting_items[key].set_value(value)
                        self._on_setting_changed(key, value)
                
                QMessageBox.information(self, "导入成功", f"设置已从 {file_path} 导入。")
                self.logger.info(f"设置已从 {file_path} 导入")
                
            except Exception as e:
                QMessageBox.critical(self, "导入失败", f"导入设置失败: {e}")
                self.logger.error(f"导入设置失败: {e}")
    
    def get_setting_value(self, key: str, default_value: Any = None) -> Any:
        """
        获取设置值
        
        Args:
            key: 设置键
            default_value: 默认值
            
        Returns:
            设置值
        """
        return self.settings.value(key, default_value)
    
    def set_setting_value(self, key: str, value: Any) -> None:
        """
        设置值
        
        Args:
            key: 设置键
            value: 值
        """
        self.settings.setValue(key, value)
        
        if key in self.setting_items:
            self.setting_items[key].set_value(value)
        
        self._on_setting_changed(key, value)
    
    def on_theme_changed(self, theme_name: str) -> None:
        """
        主题变更处理
        
        Args:
            theme_name: 新主题名称
        """
        # 更新主题设置项
        if "theme" in self.setting_items:
            self.setting_items["theme"].set_value(theme_name)
        
        self.logger.debug(f"设置界面主题已更新: {theme_name}")
    
    def cleanup(self) -> None:
        """清理资源"""
        # 确保设置已保存
        self.settings.sync()
        super().cleanup()