"""
控制器基类

提供控制器的基础功能，包括：
- 处理用户交互
- 协调Model和View之间的数据流
- 业务逻辑处理
- 命令模式支持（撤销/重做）
- 事件处理
"""

import logging
from typing import Any, Dict, List, Optional, Callable
from abc import ABC, abstractmethod

from PySide6.QtCore import QObject, Signal, QTimer
from PySide6.QtWidgets import QWidget, QUndoStack, QUndoCommand

from .models import BaseModel


class Command(QUndoCommand):
    """
    命令基类
    
    实现命令模式，支持撤销和重做操作。
    """
    
    def __init__(self, description: str = ""):
        """
        初始化命令
        
        Args:
            description: 命令描述
        """
        super().__init__(description)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def redo(self) -> None:
        """执行命令"""
        pass
    
    @abstractmethod
    def undo(self) -> None:
        """撤销命令"""
        pass


class ModelUpdateCommand(Command):
    """
    模型更新命令
    
    用于更新模型字段值的命令。
    """
    
    def __init__(self, model: BaseModel, field_name: str, 
                 new_value: Any, old_value: Any = None):
        """
        初始化模型更新命令
        
        Args:
            model: 要更新的模型
            field_name: 字段名
            new_value: 新值
            old_value: 旧值
        """
        super().__init__(f"更新 {field_name}")
        
        self.model = model
        self.field_name = field_name
        self.new_value = new_value
        self.old_value = old_value if old_value is not None else model.get_value(field_name)
    
    def redo(self) -> None:
        """执行更新"""
        self.model.set_value(self.field_name, self.new_value, validate=False)
        self.logger.debug(f"执行更新: {self.field_name} = {self.new_value}")
    
    def undo(self) -> None:
        """撤销更新"""
        self.model.set_value(self.field_name, self.old_value, validate=False)
        self.logger.debug(f"撤销更新: {self.field_name} = {self.old_value}")


class BaseController(QObject):
    """
    控制器基类
    
    提供控制器的基础功能和接口。
    """
    
    # 信号定义
    status_changed = Signal(str)  # 状态变更信号
    error_occurred = Signal(str, str)  # 错误发生信号 (错误类型, 错误信息)
    operation_completed = Signal(str, bool)  # 操作完成信号 (操作名, 是否成功)
    
    def __init__(self, model: BaseModel = None, view: QWidget = None):
        """
        初始化控制器
        
        Args:
            model: 关联的模型
            view: 关联的视图
        """
        super().__init__()
        
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 关联的模型和视图
        self._model = model
        self._view = view
        
        # 命令栈（支持撤销/重做）
        self._undo_stack = QUndoStack(self)
        
        # 事件处理器映射
        self._event_handlers: Dict[str, List[Callable]] = {}
        
        # 定时器（用于延迟操作）
        self._delayed_timer = QTimer()
        self._delayed_timer.setSingleShot(True)
        self._delayed_timer.timeout.connect(self._execute_delayed_operation)
        self._delayed_operation: Optional[Callable] = None
        
        # 控制器状态
        self._status = "initialized"
        
        # 连接信号
        self._connect_signals()
        
        self.logger.info(f"{self.__class__.__name__} 初始化完成")
    
    def _connect_signals(self) -> None:
        """连接信号和槽"""
        if self._model:
            self._model.data_changed.connect(self._on_model_data_changed)
            self._model.validation_error.connect(self._on_model_validation_error)
    
    def _on_model_data_changed(self, field_name: str, new_value: Any) -> None:
        """
        处理模型数据变更
        
        Args:
            field_name: 字段名
            new_value: 新值
        """
        self.logger.debug(f"模型数据变更: {field_name} = {new_value}")
        self._update_view_from_model(field_name, new_value)
    
    def _on_model_validation_error(self, field_name: str, error_message: str) -> None:
        """
        处理模型验证错误
        
        Args:
            field_name: 字段名
            error_message: 错误信息
        """
        self.logger.warning(f"模型验证错误: {field_name} - {error_message}")
        self.error_occurred.emit("validation_error", f"{field_name}: {error_message}")
    
    def _update_view_from_model(self, field_name: str, new_value: Any) -> None:
        """
        从模型更新视图
        
        Args:
            field_name: 字段名
            new_value: 新值
        """
        # 子类应该重写此方法来实现具体的视图更新逻辑
        pass
    
    def get_model(self) -> Optional[BaseModel]:
        """
        获取关联的模型
        
        Returns:
            模型实例或None
        """
        return self._model
    
    def set_model(self, model: BaseModel) -> None:
        """
        设置关联的模型
        
        Args:
            model: 新的模型实例
        """
        # 断开旧模型的信号
        if self._model:
            self._model.data_changed.disconnect(self._on_model_data_changed)
            self._model.validation_error.disconnect(self._on_model_validation_error)
        
        # 设置新模型
        self._model = model
        
        # 连接新模型的信号
        if self._model:
            self._model.data_changed.connect(self._on_model_data_changed)
            self._model.validation_error.connect(self._on_model_validation_error)
        
        self.logger.info("模型已更新")
    
    def get_view(self) -> Optional[QWidget]:
        """
        获取关联的视图
        
        Returns:
            视图实例或None
        """
        return self._view
    
    def set_view(self, view: QWidget) -> None:
        """
        设置关联的视图
        
        Args:
            view: 新的视图实例
        """
        self._view = view
        self.logger.info("视图已更新")
    
    def get_undo_stack(self) -> QUndoStack:
        """
        获取撤销栈
        
        Returns:
            QUndoStack实例
        """
        return self._undo_stack
    
    def execute_command(self, command: Command) -> None:
        """
        执行命令
        
        Args:
            command: 要执行的命令
        """
        self._undo_stack.push(command)
        self.logger.debug(f"命令已执行: {command.text()}")
    
    def undo(self) -> bool:
        """
        撤销操作
        
        Returns:
            是否撤销成功
        """
        if self._undo_stack.canUndo():
            self._undo_stack.undo()
            self.logger.debug("撤销操作完成")
            return True
        return False
    
    def redo(self) -> bool:
        """
        重做操作
        
        Returns:
            是否重做成功
        """
        if self._undo_stack.canRedo():
            self._undo_stack.redo()
            self.logger.debug("重做操作完成")
            return True
        return False
    
    def can_undo(self) -> bool:
        """
        是否可以撤销
        
        Returns:
            是否可以撤销
        """
        return self._undo_stack.canUndo()
    
    def can_redo(self) -> bool:
        """
        是否可以重做
        
        Returns:
            是否可以重做
        """
        return self._undo_stack.canRedo()
    
    def clear_undo_stack(self) -> None:
        """清空撤销栈"""
        self._undo_stack.clear()
        self.logger.debug("撤销栈已清空")
    
    def update_model_field(self, field_name: str, new_value: Any) -> bool:
        """
        更新模型字段（支持撤销）
        
        Args:
            field_name: 字段名
            new_value: 新值
            
        Returns:
            是否更新成功
        """
        if not self._model:
            self.logger.error("没有关联的模型")
            return False
        
        old_value = self._model.get_value(field_name)
        
        # 创建并执行命令
        command = ModelUpdateCommand(self._model, field_name, new_value, old_value)
        self.execute_command(command)
        
        return True
    
    def add_event_handler(self, event_name: str, handler: Callable) -> None:
        """
        添加事件处理器
        
        Args:
            event_name: 事件名称
            handler: 处理器函数
        """
        if event_name not in self._event_handlers:
            self._event_handlers[event_name] = []
        
        self._event_handlers[event_name].append(handler)
        self.logger.debug(f"事件处理器已添加: {event_name}")
    
    def remove_event_handler(self, event_name: str, handler: Callable) -> bool:
        """
        移除事件处理器
        
        Args:
            event_name: 事件名称
            handler: 处理器函数
            
        Returns:
            是否移除成功
        """
        if event_name in self._event_handlers:
            try:
                self._event_handlers[event_name].remove(handler)
                self.logger.debug(f"事件处理器已移除: {event_name}")
                return True
            except ValueError:
                pass
        
        return False
    
    def emit_event(self, event_name: str, *args, **kwargs) -> None:
        """
        触发事件
        
        Args:
            event_name: 事件名称
            *args: 位置参数
            **kwargs: 关键字参数
        """
        if event_name in self._event_handlers:
            for handler in self._event_handlers[event_name]:
                try:
                    handler(*args, **kwargs)
                except Exception as e:
                    self.logger.error(f"事件处理器执行失败: {e}")
                    self.error_occurred.emit("event_handler_error", str(e))
    
    def delay_operation(self, operation: Callable, delay_ms: int = 500) -> None:
        """
        延迟执行操作
        
        Args:
            operation: 要执行的操作
            delay_ms: 延迟时间（毫秒）
        """
        self._delayed_operation = operation
        self._delayed_timer.start(delay_ms)
    
    def _execute_delayed_operation(self) -> None:
        """执行延迟操作"""
        if self._delayed_operation:
            try:
                self._delayed_operation()
            except Exception as e:
                self.logger.error(f"延迟操作执行失败: {e}")
                self.error_occurred.emit("delayed_operation_error", str(e))
            finally:
                self._delayed_operation = None
    
    def get_status(self) -> str:
        """
        获取控制器状态
        
        Returns:
            当前状态
        """
        return self._status
    
    def set_status(self, status: str) -> None:
        """
        设置控制器状态
        
        Args:
            status: 新状态
        """
        if status != self._status:
            old_status = self._status
            self._status = status
            self.status_changed.emit(status)
            self.logger.debug(f"状态变更: {old_status} -> {status}")
    
    @abstractmethod
    def initialize(self) -> bool:
        """
        初始化控制器
        
        子类必须实现此方法来进行具体的初始化操作。
        
        Returns:
            是否初始化成功
        """
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """
        清理控制器
        
        子类必须实现此方法来进行清理操作。
        """
        pass


class CRUDController(BaseController):
    """
    CRUD控制器
    
    提供标准的创建、读取、更新、删除操作。
    """
    
    # 信号定义
    item_created = Signal(object)  # 项目创建信号
    item_updated = Signal(object)  # 项目更新信号
    item_deleted = Signal(object)  # 项目删除信号
    items_loaded = Signal(list)  # 项目加载信号
    
    def __init__(self, model: BaseModel = None, view: QWidget = None):
        """
        初始化CRUD控制器
        
        Args:
            model: 关联的模型
            view: 关联的视图
        """
        super().__init__(model, view)
        
        # 数据存储（简单的内存存储，实际应用中应该使用数据库）
        self._items: List[BaseModel] = []
        self._next_id = 1
    
    def initialize(self) -> bool:
        """
        初始化控制器
        
        Returns:
            是否初始化成功
        """
        self.set_status("ready")
        self.logger.info("CRUD控制器初始化完成")
        return True
    
    def cleanup(self) -> None:
        """清理控制器"""
        self._items.clear()
        self.clear_undo_stack()
        self.set_status("cleaned")
        self.logger.info("CRUD控制器清理完成")
    
    def create_item(self, data: Dict[str, Any] = None) -> Optional[BaseModel]:
        """
        创建新项目
        
        Args:
            data: 初始数据
            
        Returns:
            创建的项目或None
        """
        try:
            if data is None:
                data = {}
            
            # 设置ID
            data['id'] = self._next_id
            self._next_id += 1
            
            # 创建模型实例
            if self._model:
                item = self._model.__class__(**data)
            else:
                self.logger.error("没有关联的模型类")
                return None
            
            # 验证数据
            is_valid, errors = item.validate()
            if not is_valid:
                error_msg = "; ".join([f"{k}: {v}" for k, v in errors.items()])
                self.error_occurred.emit("validation_error", error_msg)
                return None
            
            # 添加到列表
            self._items.append(item)
            
            # 发送信号
            self.item_created.emit(item)
            self.operation_completed.emit("create", True)
            
            self.logger.info(f"项目创建成功: ID={item.get_value('id')}")
            return item
            
        except Exception as e:
            self.logger.error(f"创建项目失败: {e}")
            self.error_occurred.emit("create_error", str(e))
            self.operation_completed.emit("create", False)
            return None
    
    def read_item(self, item_id: int) -> Optional[BaseModel]:
        """
        读取项目
        
        Args:
            item_id: 项目ID
            
        Returns:
            项目实例或None
        """
        for item in self._items:
            if item.get_value('id') == item_id:
                return item
        
        self.logger.warning(f"项目不存在: ID={item_id}")
        return None
    
    def update_item(self, item_id: int, data: Dict[str, Any]) -> bool:
        """
        更新项目
        
        Args:
            item_id: 项目ID
            data: 更新数据
            
        Returns:
            是否更新成功
        """
        try:
            item = self.read_item(item_id)
            if not item:
                self.error_occurred.emit("not_found", f"项目不存在: ID={item_id}")
                return False
            
            # 更新字段
            for field_name, value in data.items():
                if field_name != 'id':  # 不允许更新ID
                    item.set_value(field_name, value)
            
            # 验证数据
            is_valid, errors = item.validate()
            if not is_valid:
                error_msg = "; ".join([f"{k}: {v}" for k, v in errors.items()])
                self.error_occurred.emit("validation_error", error_msg)
                return False
            
            # 发送信号
            self.item_updated.emit(item)
            self.operation_completed.emit("update", True)
            
            self.logger.info(f"项目更新成功: ID={item_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新项目失败: {e}")
            self.error_occurred.emit("update_error", str(e))
            self.operation_completed.emit("update", False)
            return False
    
    def delete_item(self, item_id: int) -> bool:
        """
        删除项目
        
        Args:
            item_id: 项目ID
            
        Returns:
            是否删除成功
        """
        try:
            item = self.read_item(item_id)
            if not item:
                self.error_occurred.emit("not_found", f"项目不存在: ID={item_id}")
                return False
            
            # 从列表中移除
            self._items.remove(item)
            
            # 发送信号
            self.item_deleted.emit(item)
            self.operation_completed.emit("delete", True)
            
            self.logger.info(f"项目删除成功: ID={item_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除项目失败: {e}")
            self.error_occurred.emit("delete_error", str(e))
            self.operation_completed.emit("delete", False)
            return False
    
    def list_items(self) -> List[BaseModel]:
        """
        获取所有项目
        
        Returns:
            项目列表
        """
        self.items_loaded.emit(self._items.copy())
        return self._items.copy()
    
    def search_items(self, query: str, field_name: str = None) -> List[BaseModel]:
        """
        搜索项目
        
        Args:
            query: 搜索查询
            field_name: 搜索字段，None表示搜索所有字段
            
        Returns:
            匹配的项目列表
        """
        results = []
        query_lower = query.lower()
        
        for item in self._items:
            if field_name:
                # 搜索指定字段
                value = item.get_value(field_name)
                if value and query_lower in str(value).lower():
                    results.append(item)
            else:
                # 搜索所有字段
                for field in item.get_field_names():
                    value = item.get_value(field)
                    if value and query_lower in str(value).lower():
                        results.append(item)
                        break
        
        self.logger.info(f"搜索完成: 查询='{query}', 结果数量={len(results)}")
        return results