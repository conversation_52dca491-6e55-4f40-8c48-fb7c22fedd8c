"""
数据模型基类

提供数据模型的基础功能，包括：
- 数据验证
- 序列化和反序列化
- 数据持久化接口
- 信号/槽机制
- 字段定义和类型检查
"""

import json
import logging
from typing import Any, Dict, List, Optional, Type, Union
from datetime import datetime
from abc import ABC, abstractmethod

from PySide6.QtCore import QObject, Signal, Property


class FieldValidator:
    """字段验证器"""
    
    def __init__(self, field_type: Type, required: bool = False, 
                 default: Any = None, min_length: int = None, 
                 max_length: int = None, choices: List = None):
        """
        初始化字段验证器
        
        Args:
            field_type: 字段类型
            required: 是否必填
            default: 默认值
            min_length: 最小长度
            max_length: 最大长度
            choices: 可选值列表
        """
        self.field_type = field_type
        self.required = required
        self.default = default
        self.min_length = min_length
        self.max_length = max_length
        self.choices = choices
    
    def validate(self, value: Any) -> tuple[bool, str]:
        """
        验证字段值
        
        Args:
            value: 要验证的值
            
        Returns:
            (是否有效, 错误信息)
        """
        # 检查必填
        if self.required and (value is None or value == ""):
            return False, "该字段为必填项"
        
        # 如果值为空且不是必填，使用默认值
        if value is None or value == "":
            value = self.default
            if value is None:
                return True, ""
        
        # 检查类型
        if not isinstance(value, self.field_type):
            try:
                value = self.field_type(value)
            except (ValueError, TypeError):
                return False, f"字段类型必须为 {self.field_type.__name__}"
        
        # 检查长度（对字符串和列表）
        if hasattr(value, '__len__'):
            length = len(value)
            if self.min_length is not None and length < self.min_length:
                return False, f"长度不能少于 {self.min_length}"
            if self.max_length is not None and length > self.max_length:
                return False, f"长度不能超过 {self.max_length}"
        
        # 检查可选值
        if self.choices is not None and value not in self.choices:
            return False, f"值必须在 {self.choices} 中选择"
        
        return True, ""


class BaseModel(QObject):
    """
    数据模型基类
    
    提供数据模型的基础功能，包括字段定义、验证、序列化等。
    """
    
    # 信号定义
    data_changed = Signal(str, object)  # 数据变更信号 (字段名, 新值)
    validation_error = Signal(str, str)  # 验证错误信号 (字段名, 错误信息)
    
    def __init__(self, **kwargs):
        """
        初始化模型
        
        Args:
            **kwargs: 初始数据
        """
        super().__init__()
        
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 字段定义
        self._fields: Dict[str, FieldValidator] = {}
        self._data: Dict[str, Any] = {}
        
        # 定义字段
        self._define_fields()
        
        # 设置初始数据
        self._set_initial_data(kwargs)
        
        # 验证初始数据
        self.validate()
    
    def _define_fields(self) -> None:
        """
        定义模型字段
        
        子类应该重写此方法来定义具体的字段。
        """
        # 基础字段
        self.add_field('id', int, default=0)
        self.add_field('created_at', datetime, default=datetime.now)
        self.add_field('updated_at', datetime, default=datetime.now)
    
    def add_field(self, name: str, field_type: Type, **kwargs) -> None:
        """
        添加字段定义
        
        Args:
            name: 字段名
            field_type: 字段类型
            **kwargs: 字段验证器参数
        """
        self._fields[name] = FieldValidator(field_type, **kwargs)
        
        # 设置默认值
        if name not in self._data:
            default_value = kwargs.get('default')
            if callable(default_value):
                default_value = default_value()
            self._data[name] = default_value
    
    def _set_initial_data(self, data: Dict[str, Any]) -> None:
        """
        设置初始数据
        
        Args:
            data: 初始数据字典
        """
        for key, value in data.items():
            if key in self._fields:
                self._data[key] = value
            else:
                self.logger.warning(f"未知字段: {key}")
    
    def get_field_names(self) -> List[str]:
        """
        获取所有字段名
        
        Returns:
            字段名列表
        """
        return list(self._fields.keys())
    
    def get_field_info(self, field_name: str) -> Optional[FieldValidator]:
        """
        获取字段信息
        
        Args:
            field_name: 字段名
            
        Returns:
            字段验证器或None
        """
        return self._fields.get(field_name)
    
    def get_value(self, field_name: str) -> Any:
        """
        获取字段值
        
        Args:
            field_name: 字段名
            
        Returns:
            字段值
        """
        return self._data.get(field_name)
    
    def set_value(self, field_name: str, value: Any, validate: bool = True) -> bool:
        """
        设置字段值
        
        Args:
            field_name: 字段名
            value: 新值
            validate: 是否验证
            
        Returns:
            是否设置成功
        """
        if field_name not in self._fields:
            self.logger.error(f"未知字段: {field_name}")
            return False
        
        # 验证值
        if validate:
            validator = self._fields[field_name]
            is_valid, error_msg = validator.validate(value)
            if not is_valid:
                self.validation_error.emit(field_name, error_msg)
                return False
        
        # 设置值
        old_value = self._data.get(field_name)
        self._data[field_name] = value
        
        # 更新修改时间
        if field_name != 'updated_at':
            self._data['updated_at'] = datetime.now()
        
        # 发送变更信号
        if old_value != value:
            self.data_changed.emit(field_name, value)
        
        return True
    
    def validate(self, field_name: str = None) -> tuple[bool, Dict[str, str]]:
        """
        验证数据
        
        Args:
            field_name: 要验证的字段名，None表示验证所有字段
            
        Returns:
            (是否全部有效, 错误信息字典)
        """
        errors = {}
        
        fields_to_validate = [field_name] if field_name else self._fields.keys()
        
        for name in fields_to_validate:
            if name in self._fields:
                validator = self._fields[name]
                value = self._data.get(name)
                is_valid, error_msg = validator.validate(value)
                if not is_valid:
                    errors[name] = error_msg
        
        is_all_valid = len(errors) == 0
        return is_all_valid, errors
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        Returns:
            数据字典
        """
        result = {}
        for key, value in self._data.items():
            if isinstance(value, datetime):
                result[key] = value.isoformat()
            else:
                result[key] = value
        return result
    
    def from_dict(self, data: Dict[str, Any]) -> None:
        """
        从字典加载数据
        
        Args:
            data: 数据字典
        """
        for key, value in data.items():
            if key in self._fields:
                # 处理日期时间字段
                field_type = self._fields[key].field_type
                if field_type == datetime and isinstance(value, str):
                    try:
                        value = datetime.fromisoformat(value)
                    except ValueError:
                        self.logger.warning(f"无法解析日期时间: {value}")
                        continue
                
                self.set_value(key, value, validate=False)
    
    def to_json(self) -> str:
        """
        转换为JSON字符串
        
        Returns:
            JSON字符串
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    def from_json(self, json_str: str) -> None:
        """
        从JSON字符串加载数据
        
        Args:
            json_str: JSON字符串
        """
        try:
            data = json.loads(json_str)
            self.from_dict(data)
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
    
    def clone(self) -> 'BaseModel':
        """
        克隆模型
        
        Returns:
            新的模型实例
        """
        new_instance = self.__class__()
        new_instance.from_dict(self.to_dict())
        return new_instance
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}({self.to_dict()})"
    
    def __repr__(self) -> str:
        """调试字符串表示"""
        return self.__str__()


class ModelManager(QObject):
    """
    模型管理器
    
    管理多个模型实例，提供统一的数据访问接口。
    """
    
    # 信号定义
    model_added = Signal(str, BaseModel)  # 模型添加信号
    model_removed = Signal(str)  # 模型移除信号
    model_updated = Signal(str, BaseModel)  # 模型更新信号
    
    def __init__(self):
        """初始化模型管理器"""
        super().__init__()
        
        self.logger = logging.getLogger(__name__)
        self._models: Dict[str, BaseModel] = {}
    
    def add_model(self, name: str, model: BaseModel) -> None:
        """
        添加模型
        
        Args:
            name: 模型名称
            model: 模型实例
        """
        if name in self._models:
            self.logger.warning(f"模型已存在，将被替换: {name}")
        
        self._models[name] = model
        
        # 连接模型信号
        model.data_changed.connect(lambda field, value: self.model_updated.emit(name, model))
        
        self.model_added.emit(name, model)
        self.logger.info(f"模型已添加: {name}")
    
    def remove_model(self, name: str) -> bool:
        """
        移除模型
        
        Args:
            name: 模型名称
            
        Returns:
            是否移除成功
        """
        if name not in self._models:
            self.logger.warning(f"模型不存在: {name}")
            return False
        
        del self._models[name]
        self.model_removed.emit(name)
        self.logger.info(f"模型已移除: {name}")
        return True
    
    def get_model(self, name: str) -> Optional[BaseModel]:
        """
        获取模型
        
        Args:
            name: 模型名称
            
        Returns:
            模型实例或None
        """
        return self._models.get(name)
    
    def get_all_models(self) -> Dict[str, BaseModel]:
        """
        获取所有模型
        
        Returns:
            模型字典
        """
        return self._models.copy()
    
    def clear(self) -> None:
        """清除所有模型"""
        model_names = list(self._models.keys())
        for name in model_names:
            self.remove_model(name)
        
        self.logger.info("所有模型已清除")